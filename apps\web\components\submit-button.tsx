"use client";

import { useFormStatus } from "react-dom";
import { Button } from "@workspace/ui/components/button";

export function SubmitButton({
	children,
	...props
}: {
	children: React.ReactNode;
} & React.ComponentProps<typeof Button>) {
	const { pending } = useFormStatus();

	return (
		<Button type="submit" disabled={pending} {...props}>
			{pending ? "로그인 중..." : children}
		</Button>
	);
}
