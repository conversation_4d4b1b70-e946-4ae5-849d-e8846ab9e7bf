import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 확인 대화상자 도구 (Human-in-the-Loop)
 * AI SDK 5 패턴에 따라 execute 함수 없이 구현
 * 프론트엔드에서 사용자 확인을 받은 후 결과를 처리합니다.
 */
export const confirmWithCheckboxTool: AISDKToolWrapper = {
  description: `사용자에게 확인/취소 대화상자를 표시합니다.

기능:
- 예/아니오 또는 확인/취소 UI 제공
- Human-in-the-Loop 패턴으로 사용자 승인 필요
- 체크박스 옵션 지원
- 커스텀 버튼 텍스트 지원

사용 예시:
- {"message": "이 레이어를 삭제하시겠습니까?", "confirmText": "삭제", "cancelText": "취소"}
- {"message": "데이터를 저장하시겠습니까?", "showCheckbox": true, "checkboxText": "다시 묻지 않기"}
- {"message": "작업을 계속하시겠습니까?"}`,

  parameters: z.object({
    message: z.string().describe("사용자에게 보여줄 확인 메시지"),
    confirmText: z.string().optional().default("확인").describe("확인 버튼 텍스트"),
    cancelText: z.string().optional().default("취소").describe("취소 버튼 텍스트"),
    showCheckbox: z.boolean().optional().default(false).describe("체크박스 표시 여부"),
    checkboxText: z.string().optional().describe("체크박스 텍스트"),
    defaultChecked: z.boolean().optional().default(false).describe("체크박스 기본 선택 상태"),
  }),

  // AI SDK 5 Human-in-the-Loop 패턴: execute 함수 제거
  // 프론트엔드에서 사용자 확인을 받은 후 결과 처리

  experimental_toToolResultContent: (result: any) => {
    // 사용자가 확인/취소를 선택한 경우
    if (result.confirmed !== undefined) {
      const action = result.confirmed ? "확인됨" : "취소됨";
      let message = `사용자 선택: ${action}`;
      
      if (result.checkboxChecked !== undefined) {
        message += ` (체크박스: ${result.checkboxChecked ? "선택됨" : "선택 안됨"})`;
      }

      return [
        {
          type: "text" as const,
          text: message,
        },
      ];
    }

    // 사용자 확인 대기 중인 경우
    return [
      {
        type: "text" as const,
        text: result.message || "사용자 확인을 기다리고 있습니다.",
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const confirmWithCheckboxDefinition: MCPToolDefinition = createMCPTool(
  "confirmWithCheckbox",
  confirmWithCheckboxTool,
  {
    title: "확인 대화상자",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
    openWorldHint: true, // 사용자 선택에 따라 다양한 결과
  }
);