"use client";

import { useRouter } from "next/navigation";
import React, { useActionState, useEffect, useState } from "react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";

import { login, LoginActionState } from "../api/auth/actions";
import { AuthForm } from "@/components/auth-form";
import { SubmitButton } from "@/components/submit-button";
import { Badge } from "@workspace/ui/components/badge";

export default function Page() {
	const router = useRouter();
	const { update } = useSession();
	const [email, setEmail] = useState("admin");
	const [state, formAction] = useActionState<LoginActionState, FormData>(
		login,
		{
			status: "idle",
		},
	);

	useEffect(() => {
		if (state.status === "failed") {
			toast.error("로그인에 실패했습니다. 아이디와 비밀번호를 확인해주세요.");
		} else if (state.status === "invalid_data") {
			toast.error("로그인에 실패했습니다. 아이디와 비밀번호를 확인해주세요.");
		} else if (state.status === "success") {
			// 세션 업데이트 후 리다이렉트
			update().then(() => {
				router.push("/");
			});
		}
	}, [state.status, router, update]);

	const handleSubmit = (formData: FormData) => {
		setEmail(formData.get("email") as string);
		formAction(formData);
	};

	return (
		<div className="flex h-screen w-screen items-center justify-center bg-background">
			<div className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-12">
				<div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
					<h3 className="flex text-xl font-semibold gap-2 dark:text-zinc-50">
						{"로그인"}
						<Badge variant={"secondary"}>GeOn</Badge>
					</h3>
					<p className="text-sm text-gray-500 dark:text-zinc-400">
						{"admin 계정으로 로그인하세요."}
					</p>
				</div>
				<AuthForm action={handleSubmit} defaultEmail={email}>
					<SubmitButton>Sign in</SubmitButton>
				</AuthForm>
{/* 
				<div className="px-4 sm:px-16">
					<div className="relative">
						<div className="absolute inset-0 flex items-center">
							<span className="w-full border-t" />
						</div>
						<div className="relative flex justify-center text-xs uppercase">
							<span className="bg-background px-2 text-muted-foreground">또는</span>
						</div>
					</div>

					<Button
						variant="outline"
						className="w-full mt-4"
						onClick={() => signIn("mappick")}
					>
						<svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
							<path
								fill="currentColor"
								d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
							/>
						</svg>
						Mappick으로 로그인
					</Button>
				</div> */}
			</div>
		</div>
	);
}
