// 모든 도구 정의 import
import { 
  searchAddressDefinition, 
  searchOriginDefinition, 
  searchDestinationDefinition 
} from './address/search.js';
import { searchCoordDefinition } from './address/coord.js';
import { searchDirectionsDefinition } from './address/directions.js';

import { 
  getBasemapListDefinition, 
  changeBasemapDefinition 
} from './map/basemap.js';
import { setMapCenterDefinition } from './map/center.js';
import { setMapZoomDefinition } from './map/zoom.js';
import { moveMapByDirectionDefinition } from './map/move.js';

import { 
  getLayerListDefinition, 
  removeLayerDefinition 
} from './layer/list.js';
import { 
  updateLayerStyleDefinition, 
  generateCategoricalStyleDefinition 
} from './layer/style.js';
import { createLayerFilterDefinition } from './layer/filter.js';
import { getLayerAttributesCountDefinition } from './layer/attributes.js';

import { chooseOptionDefinition } from './interaction/choose.js';
import { getUserInputDefinition } from './interaction/input.js';
import { confirmWithCheckboxDefinition } from './interaction/confirm.js';

import type { MCPToolDefinition } from './shared/types.js';

/**
 * 모든 MCP 도구 정의
 * Legacy GEON API 기반으로 구현
 */
export const mcpTools: Record<string, MCPToolDefinition> = {
  // Address 도구들 (4개)
  searchAddress: searchAddressDefinition,
  searchOrigin: searchOriginDefinition,
  searchDestination: searchDestinationDefinition,
  searchCoord: searchCoordDefinition,
  searchDirections: searchDirectionsDefinition,

  // Map 도구들 (5개)
  getBasemapList: getBasemapListDefinition,
  changeBasemap: changeBasemapDefinition,
  setMapCenter: setMapCenterDefinition,
  setMapZoom: setMapZoomDefinition,
  moveMapByDirection: moveMapByDirectionDefinition,

  // Layer 도구들 (6개)
  getLayerList: getLayerListDefinition,
  removeLayer: removeLayerDefinition,
  updateLayerStyle: updateLayerStyleDefinition,
  generateCategoricalStyle: generateCategoricalStyleDefinition,
  createLayerFilter: createLayerFilterDefinition,
  getLayerAttributesCount: getLayerAttributesCountDefinition,

  // Human-in-the-Loop 도구들 (3개)
  chooseOption: chooseOptionDefinition,
  getUserInput: getUserInputDefinition,
  confirmWithCheckbox: confirmWithCheckboxDefinition,
};

/**
 * AI SDK 도구 매핑 (런타임에서 사용)
 * 실제 실행 로직을 포함한 도구들
 */
export const aiSDKTools = {
  // Address 도구들
  searchAddress: () => import('./address/search.js').then(m => m.searchAddressTool),
  searchOrigin: () => import('./address/search.js').then(m => m.searchOriginTool),
  searchDestination: () => import('./address/search.js').then(m => m.searchDestinationTool),
  searchCoord: () => import('./address/coord.js').then(m => m.searchCoordTool),
  searchDirections: () => import('./address/directions.js').then(m => m.searchDirectionsTool),

  // Map 도구들
  getBasemapList: () => import('./map/basemap.js').then(m => m.getBasemapListTool),
  changeBasemap: () => import('./map/basemap.js').then(m => m.changeBasemapTool),
  setMapCenter: () => import('./map/center.js').then(m => m.setMapCenterTool),
  setMapZoom: () => import('./map/zoom.js').then(m => m.setMapZoomTool),
  moveMapByDirection: () => import('./map/move.js').then(m => m.moveMapByDirectionTool),

  // Layer 도구들
  getLayerList: () => import('./layer/list.js').then(m => m.getLayerListTool),
  removeLayer: () => import('./layer/list.js').then(m => m.removeLayerTool),
  updateLayerStyle: () => import('./layer/style.js').then(m => m.updateLayerStyleTool),
  generateCategoricalStyle: () => import('./layer/style.js').then(m => m.generateCategoricalStyleTool),
  createLayerFilter: () => import('./layer/filter.js').then(m => m.createLayerFilterTool),
  getLayerAttributesCount: () => import('./layer/attributes.js').then(m => m.getLayerAttributesCountTool),

  // Human-in-the-Loop 도구들
  chooseOption: () => import('./interaction/choose.js').then(m => m.chooseOptionTool),
  getUserInput: () => import('./interaction/input.js').then(m => m.getUserInputTool),
  confirmWithCheckbox: () => import('./interaction/confirm.js').then(m => m.confirmWithCheckboxTool),
};

/**
 * 도구 목록을 MCP 형식으로 변환
 */
export function getToolsList(): Array<{
  name: string;
  description: string;
  inputSchema: any;
  outputSchema?: any;
  annotations?: any;
}> {
  return Object.values(mcpTools).map(tool => ({
    name: tool.name,
    description: tool.description,
    inputSchema: tool.inputSchema,
    outputSchema: tool.outputSchema,
    annotations: tool.annotations,
  }));
}

/**
 * 특정 도구 정의 조회
 */
export function getToolDefinition(name: string): MCPToolDefinition | undefined {
  return mcpTools[name];
}

/**
 * 도구 이름 목록 조회
 */
export function getToolNames(): string[] {
  return Object.keys(mcpTools);
}

/**
 * AI SDK 도구 인스턴스 가져오기 (런타임)
 */
export async function getAISDKTool(name: string) {
  const toolLoader = aiSDKTools[name as keyof typeof aiSDKTools];
  if (!toolLoader) {
    throw new Error(`Unknown tool: ${name}`);
  }
  return await toolLoader();
}
