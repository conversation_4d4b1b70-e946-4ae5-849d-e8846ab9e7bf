import { z } from 'zod';
import { createMCPTool, optimizeAddressResult } from '../shared/utils.js';
import { createGeonAddrgeoClient } from '@geon-query/model/restapi/addrgeo';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 좌표 검색 도구
 * 경위도 좌표로 해당 위치의 주소 정보를 검색합니다 (역지오코딩)
 */
export const searchCoordTool: AISDKToolWrapper = {
  description: `경위도 좌표로 해당 위치의 주소 정보를 검색합니다.

기능:
- 위도, 경도 좌표를 입력하여 주소 정보를 얻을 수 있습니다
- 역지오코딩 기능을 제공합니다
- 좌표를 주소로 변환

사용 예시:
- {"lat": "37.4000431431", "lng": "126.9666143718"}
- {"lat": "37.5665", "lng": "126.9780"}`,

  parameters: z.object({
    lat: z.string().describe("위도 (예: 37.4000431431)"),
    lng: z.string().describe("경도 (예: 126.9666143718)"),
  }),

  execute: async (args) => {
    try {
      // geon-query/model 클라이언트 생성
      const apiKey = process.env.GEON_API_KEY || "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";
      const baseUrl = process.env.GEON_API_BASE_URL || "https://city.geon.kr/api/";
      
      const addressClient = createGeonAddrgeoClient({
        baseUrl,
        crtfckey: apiKey,
      });

      // 좌표 → 주소 변환 API 호출
      const result = await addressClient.address.coord({
        lat: args.lat,
        lng: args.lng,
      });

      return result;
    } catch (error: any) {
      console.error("좌표 검색 실행 실패:", error);
      return {
        error: `좌표 검색 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    // 좌표 검색 결과 최적화
    if (!result.result?.jusoList?.length) {
      return [
        {
          type: "text" as const,
          text: "좌표 검색 결과가 없습니다. 다른 좌표로 다시 검색해보세요.",
        },
      ];
    }

    const addresses = result.result.jusoList;
    const addressCount = addresses.length;

    // 최대 3개까지만 LLM에 전달
    const topAddresses = addresses.slice(0, 3).map((addr: any) => ({
      roadAddr: addr.roadAddr,
      buildName: addr.buildName || addr.poiName,
      buildLo: addr.buildLo, // 경도 (X좌표)
      buildLa: addr.buildLa, // 위도 (Y좌표)
    }));

    const summary = `좌표 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;

    let addressInfo = topAddresses
      .map(
        (addr: any, index: number) =>
          `${index + 1}. ${addr.roadAddr}${
            addr.buildName ? ` (${addr.buildName})` : ""
          } - 좌표: ${addr.buildLo},${addr.buildLa}`
      )
      .join("\n");

    if (addressCount > 3) {
      addressInfo += `\n... 외 ${addressCount - 3}개 추가 결과`;
    }

    return [
      {
        type: "text" as const,
        text: `${summary}\n\n${addressInfo}`,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const searchCoordDefinition: MCPToolDefinition = createMCPTool(
  "searchCoord",
  searchCoordTool,
  {
    title: "좌표 검색 (역지오코딩)",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);