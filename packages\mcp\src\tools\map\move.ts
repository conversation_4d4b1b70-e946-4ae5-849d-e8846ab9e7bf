import { z } from 'zod';
import { createMCPTool, parseDistance } from '../shared/utils.js';
import { MapSchemas } from '../shared/validators.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 지도 방향 이동 도구
 */
export const moveMapByDirectionTool: AISDKToolWrapper = {
  description: `지도를 특정 방향으로 이동합니다.

지원하는 방향:
- north, up, 위, 북쪽: 북쪽으로 이동
- south, down, 아래, 남쪽: 남쪽으로 이동
- east, right, 오른쪽, 동쪽: 동쪽으로 이동
- west, left, 왼쪽, 서쪽: 서쪽으로 이동

거리 형식:
- "500m", "1km", "2000m" 등의 형태로 입력
- 단위가 없으면 미터(m)로 간주
- 기본값: "500m"

좌표계 지원:
- EPSG:5186 (Korea 2000 / Central Belt 2010): 미터 단위 직접 계산
- 정확한 거리 이동을 위해 투영 좌표계 사용

사용 예시:
- {"direction": "north", "distance": "500m"}
- {"direction": "east", "distance": "1km"}
- {"direction": "up", "distance": "2000m"}`,

  parameters: z.object({
    direction: MapSchemas.direction,
    distance: MapSchemas.distance.optional().default("500m"),
  }),

  execute: async ({ direction, distance }) => {
    try {
      const distanceInMeters = parseDistance(distance);

      // EPSG:5186 좌표계에서는 미터 단위로 직접 계산
      // 이 좌표계는 한국 중부 지역에 최적화된 투영 좌표계로 미터 단위 사용
      let deltaX = 0; // 동서 방향 (X축)
      let deltaY = 0; // 남북 방향 (Y축)

      switch (direction) {
        case "north":
        case "up":
          deltaY = distanceInMeters; // 북쪽으로 이동 (Y 증가)
          break;
        case "south":
        case "down":
          deltaY = -distanceInMeters; // 남쪽으로 이동 (Y 감소)
          break;
        case "east":
        case "right":
          deltaX = distanceInMeters; // 동쪽으로 이동 (X 증가)
          break;
        case "west":
        case "left":
          deltaX = -distanceInMeters; // 서쪽으로 이동 (X 감소)
          break;
      }

      const directionNames = {
        north: "북쪽",
        south: "남쪽",
        east: "동쪽",
        west: "서쪽",
        up: "위쪽",
        down: "아래쪽",
        left: "왼쪽",
        right: "오른쪽",
      };

      return {
        success: true,
        direction,
        deltaX, // EPSG:5186에서는 X, Y 좌표 사용
        deltaY,
        distance: distanceInMeters,
        coordinateSystem: "EPSG:5186",
        message: `지도가 ${directionNames[direction as keyof typeof directionNames]}으로 ${distance} 이동되었습니다.`,
      };
    } catch (error: any) {
      console.error("지도 방향 이동 실패:", error);
      return {
        success: false,
        error: `지도 방향 이동 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: result.message,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const moveMapByDirectionDefinition: MCPToolDefinition = createMCPTool(
  "moveMapByDirection",
  moveMapByDirectionTool,
  {
    title: "지도 방향 이동",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);