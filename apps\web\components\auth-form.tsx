import Form from "next/form";
import { Input } from "@workspace/ui/components/input";

interface AuthFormProps {
	action: (formData: FormData) => void;
	children: React.ReactNode;
	defaultEmail?: string;
}

export function AuthForm({
	action,
	children,
	defaultEmail = "",
}: AuthFormProps) {
	return (
		<Form action={action} className="flex flex-col gap-4 px-4 sm:px-16">
			<div className="flex flex-col gap-2">
				<label
					htmlFor="id"
					className="text-zinc-600 font-normal dark:text-zinc-400"
				>
					아이디
				</label>

				<Input
					id="id"
					name="id"
					className="bg-muted text-md md:text-sm"
					type="text"
					placeholder="admin"
					autoComplete="username"
					required
					defaultValue={defaultEmail}
				/>

				<label
					htmlFor="password"
					className="text-zinc-600 font-normal dark:text-zinc-400"
				>
					비밀번호
				</label>

				<Input
					id="password"
					name="password"
					className="bg-muted text-md md:text-sm"
					type="password"
					required
				/>
			</div>

			{children}
		</Form>
	);
}
