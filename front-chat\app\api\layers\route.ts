import { NextRequest, NextResponse } from "next/server";
import { getApiConfig, addAuthToParams, getApiHeaders, getApiUserId } from "@/lib/api-config";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const config = getApiConfig();

    // URL 파라미터 구성
    const params = new URLSearchParams();

    // 필수 파라미터 - 항상 geonuser 계정 사용 (프론트엔드 로그인 계정과 무관)
    params.append("userId", getApiUserId(config));
    params.append("holdDataSeCode", searchParams.get("holdDataSeCode") || "0");
    params.append("pageIndex", searchParams.get("pageIndex") || "1");
    params.append("pageSize", searchParams.get("pageSize") || "10");

    // 선택적 파라미터
    const searchTxt = searchParams.get("searchTxt");
    if (searchTxt && searchTxt.trim() !== "") {
      params.append("searchTxt", searchTxt.trim());
    }

    const lyrTySeCode = searchParams.get("lyrTySeCode");
    if (lyrTySeCode && lyrTySeCode.trim() !== "") {
      params.append("lyrTySeCode", lyrTySeCode.trim());
    }

    // 인증 정보 추가
    addAuthToParams(params, config);

    // 외부 API 호출
    const response = await fetch(
      `${config.baseUrl}/smt/layer/info/list?${params.toString()}`,
      {
        method: "GET",
        headers: getApiHeaders(config),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("API request failed with data", errorData);
      return NextResponse.json(
        { error: `API request failed with status ${response.status}` },
        { status: response.status }
      );
    }

    const data = await response.json();

    if (!data || !data.result) {
      return NextResponse.json(
        { error: "레이어 목록 조회 실패: 응답 데이터가 없습니다." },
        { status: 500 }
      );
    }

    return NextResponse.json(data);
  } catch (error: any) {
    console.error("Layer list API error:", error);
    return NextResponse.json(
      { error: `레이어 목록 조회 실패: ${error.message}` },
      { status: 500 }
    );
  }
}
