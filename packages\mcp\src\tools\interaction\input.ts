import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 사용자 입력 요청 도구 (Human-in-the-Loop)
 * AI SDK 5 패턴에 따라 execute 함수 없이 구현
 * 프론트엔드에서 사용자 입력을 받은 후 결과를 처리합니다.
 */
export const getUserInputTool: AISDKToolWrapper = {
  description: `사용자에게 텍스트 입력을 요청합니다.

기능:
- 자유 텍스트 입력 UI 제공
- Human-in-the-Loop 패턴으로 사용자 승인 필요
- 플레이스홀더 텍스트 지원
- 필수/선택 입력 설정

사용 예시:
- {"message": "검색할 주소를 입력하세요", "placeholder": "예: 서울특별시 중구 세종대로 110"}
- {"message": "레이어 이름을 입력하세요", "required": true}
- {"message": "추가 설명을 입력하세요 (선택사항)", "required": false}`,

  parameters: z.object({
    message: z.string().describe("사용자에게 보여줄 안내 문구"),
    placeholder: z.string().optional().describe("입력 필드 플레이스홀더 텍스트"),
    required: z.boolean().optional().default(true).describe("필수 입력 여부"),
    inputType: z
      .enum(["text", "number", "email", "url"])
      .optional()
      .default("text")
      .describe("입력 타입"),
  }),

  // AI SDK 5 Human-in-the-Loop 패턴: execute 함수 제거
  // 프론트엔드에서 사용자 입력을 받은 후 결과 처리

  experimental_toToolResultContent: (result: any) => {
    // 사용자가 입력을 완료한 경우
    if (result.userInput !== undefined) {
      return [
        {
          type: "text" as const,
          text: `입력 완료: ${result.userInput}`,
        },
      ];
    }

    // 사용자 입력 대기 중인 경우
    return [
      {
        type: "text" as const,
        text: result.message || "입력을 기다리고 있습니다.",
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const getUserInputDefinition: MCPToolDefinition = createMCPTool(
  "getUserInput",
  getUserInputTool,
  {
    title: "사용자 입력 요청",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
    openWorldHint: true, // 사용자 입력에 따라 다양한 결과
  }
);