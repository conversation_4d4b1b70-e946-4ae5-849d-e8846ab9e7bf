/**
 * Legacy 도구 실행 핸들러
 * 
 * 새로 마이그레이션된 도구들을 실행하는 핸들러
 * Legacy GEON API 기반으로 구현
 */

import { CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import { getAISDKTool } from '../tools/definitions.js';

/**
 * 표준화된 에러 응답 생성
 */
function createErrorResponse(error: Error | string): CallToolResult {
  const errorMessage = error instanceof Error ? error.message : error;
  console.error('Tool execution error:', errorMessage);

  return {
    content: [
      {
        type: 'text',
        text: `Error: ${errorMessage}`,
      },
    ],
    isError: true,
  };
}

/**
 * 표준화된 성공 응답 생성
 */
function createSuccessResponse(data: any, toolResult?: any): CallToolResult {
  // AI SDK 도구의 experimental_toToolResultContent 사용
  if (toolResult && typeof toolResult.experimental_toToolResultContent === 'function') {
    const content = toolResult.experimental_toToolResultContent(data);
    return {
      content: content || [{ type: 'text', text: JSON.stringify(data, null, 2) }],
      isError: false,
    };
  }

  // 기본 처리
  return {
    content: [
      {
        type: 'text',
        text: typeof data === 'string' ? data : JSON.stringify(data, null, 2),
      },
    ],
    isError: false,
  };
}

/**
 * Legacy 기반 도구 실행 핸들러
 */
export async function executeLegacyTool(
  toolName: string,
  args: any,
  extra?: any
): Promise<CallToolResult> {
  try {
    console.log(`[Legacy Tool] Executing: ${toolName}`, args);

    // 동적으로 AI SDK 도구 가져오기
    const aiTool = await getAISDKTool(toolName);
    
    if (!aiTool) {
      throw new Error(`Tool '${toolName}' not found in legacy tools`);
    }

    // AI SDK 도구 실행
    if (aiTool.execute && typeof aiTool.execute === 'function') {
      // execute 함수가 있는 일반 도구
      const result = await aiTool.execute(args);
      return createSuccessResponse(result, aiTool);
    } else {
      // Human-in-the-Loop 도구 (execute 없음)
      // 프론트엔드에서 사용자 승인 필요
      const pendingResult = {
        pending: true,
        toolName,
        args,
        message: `${toolName} requires user interaction`,
      };
      return createSuccessResponse(pendingResult, aiTool);
    }

  } catch (error: any) {
    console.error(`[Legacy Tool] Error executing ${toolName}:`, error);
    return createErrorResponse(error);
  }
}