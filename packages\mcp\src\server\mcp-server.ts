/**
 * MCP 서버 생성 및 설정
 *
 * 이 모듈은 MCP 서버의 핵심 로직을 담당합니다:
 * 1. 서버 인스턴스 생성
 * 2. 환경변수 기반 OAuth/Legacy 모드 구분
 * 3. 도구 목록 핸들러 설정
 * 4. 도구 실행 핸들러 설정
 */

import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import {
	CallToolRequestSchema,
	ListToolsRequestSchema,
} from "@modelcontextprotocol/sdk/types.js";
import { zodToJsonSchema } from "zod-to-json-schema";
import * as geonAiTools from "@geon-ai/tools";

import { getToolsList, getToolNames } from "../tools/definitions.js";
import { executeOAuthTool } from "../handlers/oauth-tools.js";
import { executeLegacyTool } from "../handlers/legacy-tools.js";

// 환경변수로 OAuth 지원 여부 결정 - 웹앱에서는 API 키 모드 사용
const OAUTH_ENABLED = process.env.MCP_OAUTH_ENABLED === "true"; // 기본값: false (웹앱용)
const LEGACY_ENABLED = process.env.MCP_LEGACY_ENABLED !== "false"; // 기본값: true (웹앱용)

console.log(
	`[MCP Server] OAuth 모드: ${OAUTH_ENABLED ? "활성화" : "비활성화"}`,
);
console.log(
	`[MCP Server] Legacy 모드: ${LEGACY_ENABLED ? "활성화" : "비활성화"}`,
);

/**
 * MCP 서버 인스턴스 생성
 */
export function createMCPServer(): Server {
	const server = new Server(
		{
			name: "geon-mcp-server",
			version: "1.13.2",
		},
		{
			capabilities: {
				tools: {},
			},
		},
	);



	// 도구 목록 핸들러 설정
	setupToolsListHandler(server);

	// 도구 실행 핸들러 설정
	setupToolCallHandler(server);

	return server;
}

/**
 * 도구 목록 핸들러 설정
 * 환경변수에 따라 OAuth/Legacy 도구를 선택적으로 반환
 */
function setupToolsListHandler(server: Server): void {
	server.setRequestHandler(ListToolsRequestSchema, async () => {
		const allTools: any[] = [];

		// OAuth 도구 추가 (기본 비활성화)
		if (OAUTH_ENABLED) {
			// 기존 OAuth 도구들 (3개만)
			const oauthTools = [
				{
					name: 'geon_search_coord',
					description: '경위도 좌표로 해당 위치의 주소 정보를 검색합니다.',
					inputSchema: zodToJsonSchema(require('zod').z.object({
						lat: require('zod').z.string().describe("위도 (예: 37.4000431431)"),
						lng: require('zod').z.string().describe("경도 (예: 126.9666143718)")
					})),
				}
			];
			allTools.push(...oauthTools);
			console.log(`[MCP Server] OAuth 도구 ${oauthTools.length}개 로드됨`);
		}

		// 새로운 Legacy 도구 추가 (기본 활성화)
		if (LEGACY_ENABLED) {
			const newLegacyTools = getToolsList();
			allTools.push(...newLegacyTools);
			console.log(`[MCP Server] 새 Legacy 도구 ${newLegacyTools.length}개 로드됨`);
		}

		console.log(`[MCP Server] 총 ${allTools.length}개 도구 제공`);

		return {
			tools: allTools,
		};
	});
}

/**
 * 도구 실행 핸들러 설정
 * 환경변수에 따라 OAuth/Legacy 도구 실행 방식 결정
 */
function setupToolCallHandler(server: Server): void {
	server.setRequestHandler(CallToolRequestSchema, async (request, extra) => {
		const toolName = request.params.name;
		const args = request.params.arguments || {};

		try {
			// OAuth 모드가 활성화된 경우 OAuth 도구 처리
			if (OAUTH_ENABLED) {
				const oauthToolResult = await tryExecuteOAuthTool(
					toolName,
					args,
					extra,
				);
				if (oauthToolResult) {
					return oauthToolResult;
				}
			}

			// Legacy 모드가 활성화된 경우 새 도구 먼저 시도
			if (LEGACY_ENABLED) {
				// 1. 새로 마이그레이션된 도구들 시도
				const newLegacyResult = await tryExecuteNewLegacyTool(
					toolName,
					args,
					extra,
				);
				if (newLegacyResult) {
					return newLegacyResult;
				}

				// 2. 기존 @geon-ai/tools 도구들 시도 (하위 호환성)
				return await executeOldLegacyTool(toolName, args);
			}

			// 둘 다 비활성화되었거나 도구를 찾을 수 없는 경우
			throw new Error(`Tool '${toolName}' not found or not enabled`);
		} catch (error: any) {
			console.error(`Error executing tool ${toolName}:`, error);
			return {
				content: [
					{
						type: "text",
						text: `Tool execution failed: ${error.message}`,
					},
				],
				isError: true,
			};
		}
	});
}

/**
 * OAuth 도구 실행 시도
 * 기존 OAuth 도구들 (소수만 유지)
 */
async function tryExecuteOAuthTool(toolName: string, args: any, extra: any) {
	const oauthToolNames = ['geon_search_coord']; // 기존 OAuth 도구들

	if (oauthToolNames.includes(toolName)) {
		console.log(`[MCP Server] OAuth 도구 실행: ${toolName}`);
		return await executeOAuthTool(toolName, args, extra);
	}

	return null;
}

/**
 * 새로운 Legacy 도구 실행 시도
 */
async function tryExecuteNewLegacyTool(toolName: string, args: any, extra: any) {
	const newLegacyToolNames = getToolNames(); // 새로 마이그레이션된 도구들

	if (newLegacyToolNames.includes(toolName)) {
		console.log(`[MCP Server] 새 Legacy 도구 실행: ${toolName}`);
		return await executeLegacyTool(toolName, args, extra);
	}

	return null;
}

/**
 * 기존 레거시 도구 실행 (@geon-ai/tools)
 */
async function executeOldLegacyTool(toolName: string, args: any) {
	const tool = (geonAiTools as any)[toolName];

	if (!tool) {
		throw new Error(`Old legacy tool ${toolName} not found`);
	}

	console.log(`[MCP Server] 구 Legacy 도구 실행: ${toolName}`);

	const parsedArgs = tool.parameters.parse(args);
	const result = await tool.execute(parsedArgs, {
		messages: [],
	});

	// 에러 결과 처리
	if (typeof result === "object" && result && "error" in result) {
		return {
			isError: true,
			content: [
				{
					type: "text",
					text: result.error,
				},
			],
		};
	}

	// 커스텀 콘텐츠 변환 처리
	if (tool.experimental_toToolResultContent) {
		const content = tool.experimental_toToolResultContent(result);
		return { content };
	}

	// 기본 JSON 응답
	return {
		content: [
			{
				type: "text",
				text: JSON.stringify(result, null, 2),
			},
		],
	};
}
