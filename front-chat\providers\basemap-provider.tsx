"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from "react";
import { UseMapReturn } from "@geon-map/odf";

type BasemapId = 'eMapBasic' | 'eMapAIR' | 'eMapColor' | 'eMapWhite';

interface BasemapContextType {
  currentBasemap: BasemapId;
  setCurrentBasemap: (basemap: BasemapId) => void;
  changeBasemap: (basemap: BasemapId) => void;
}

const BasemapContext = createContext<BasemapContextType | null>(null);

interface BasemapProviderProps {
  children: React.ReactNode;
  mapState?: UseMapReturn;
}

export function BasemapProvider({ children, mapState }: BasemapProviderProps) {
  const [currentBasemap, setCurrentBasemap] = useState<BasemapId>('eMapBasic');

  // ODF 맵이 초기화되면 배경지도 변경 콜백 설정
  useEffect(() => {
    if (!mapState?.map) return;

    const map = mapState.map;
    
    // basemapControl이 있는지 확인하고 콜백 설정
    if (map.basemapControl && typeof map.basemapControl.setSwitchBaseLayerCallback === 'function') {
      map.basemapControl.setSwitchBaseLayerCallback((beforeLayer: string, afterLayer: string) => {
        setCurrentBasemap(afterLayer as BasemapId);
      });
    }

    // 초기 배경지도 상태 설정 (가능한 경우)
    if (map.basemapControl && typeof map.basemapControl.getPresentBaseGrpKey === 'function') {
      try {
        const currentKey = map.basemapControl.getPresentBaseGrpKey();
        if (currentKey && ['eMapBasic', 'eMapAIR', 'eMapColor', 'eMapWhite'].includes(currentKey)) {
          setCurrentBasemap(currentKey as BasemapId);
        }
      } catch (error) {
        console.warn('Failed to get current basemap:', error);
      }
    }
  }, [mapState?.map]);

  const changeBasemap = useCallback((basemap: BasemapId) => {
    if (!mapState?.view?.setBasemap) {
      console.error("map.setBasemap is not available or map not ready.");
      return;
    }

    // ODF 맵에서 배경지도 변경 (콜백에서 상태 업데이트됨)
    mapState.view.setBasemap(basemap);
    setCurrentBasemap(basemap);
  }, [mapState]);

  const contextValue: BasemapContextType = {
    currentBasemap,
    setCurrentBasemap,
    changeBasemap,
  };

  return (
    <BasemapContext.Provider value={contextValue}>
      {children}
    </BasemapContext.Provider>
  );
}

export function useBasemap(): BasemapContextType {
  const context = useContext(BasemapContext);
  if (!context) {
    throw new Error('useBasemap must be used within a BasemapProvider');
  }
  return context;
}
