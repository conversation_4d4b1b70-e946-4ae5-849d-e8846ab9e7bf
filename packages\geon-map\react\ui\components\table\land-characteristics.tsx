import { APIRequestType, EstateClient } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function LandCharacteristicsTable({
  pnu,
  crtfckey,
  numOfRows: nor,
  pageNo: pn,
  client,
}: APIRequestType<EstateClient["land"]["characteristics"]> & {
  client: EstateClient;
}) {
  // TODO: Pagination States
  const [numOfRows] = React.useState<number>(nor);
  const [pageNo] = React.useState<number>(pn);

  // query
  const { data, isError, error, isLoading } = useAppQuery({
    queryKey: ["land/characteristics", pnu],
    queryFn: () =>
      client.land.characteristics({ pnu, crtfckey, numOfRows, pageNo }),
  });

  // format result
  const result = React.useMemo(() => {
    if (!data) return undefined;
    return data.result as {
      resultList: {
        prposArea1Nm: string; // 용도지역명 1
        prposArea2Nm: string; // 용도지역명 2
        ladUseSittnNm: string; // 토지 이용 상황
        tpgrphHgCodeNm: string; // 지형 높이 (m)
        tpgrphFrmCodeNm: string; // 지형 형상
        roadSideCodeNm: string; // 도로 접면
        pblntfPclnd: string; // 공시지가 (원)
        [k: string]: string;
      }[];
    };
  }, [data]);

  // error handling
  if (isError)
    return (
      <div className="w-full flex justify-center align-middle">
        Error loading land data: {error as string}
      </div>
    );

  return (
    <Table className="w-full">
      <TableHeader>
        <TableRow>
          <TableHead className="font-bold text-center">용도지역</TableHead>
          <TableHead className="font-bold text-center">용도지역 상세</TableHead>
          <TableHead className="font-bold text-center">
            토지 이용 상황
          </TableHead>
          <TableHead className="font-bold text-center">지형 높이</TableHead>
          <TableHead className="font-bold text-center">지형 형상</TableHead>
          <TableHead className="font-bold text-center">도로 접면</TableHead>
          <TableHead className="font-bold text-center">공시지가 (원)</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {isLoading ? (
          // If is loading:
          <TableRow>
            <TableCell colSpan={8} className="text-center">
              Loading ...
            </TableCell>
          </TableRow>
        ) : result ? (
          // If there is result:
          result.resultList.map((res, idx) => (
            <TableRow key={`land-characteristics-${idx}`}>
              <TableCell className="text-center">{res.prposArea1Nm}</TableCell>
              <TableCell className="text-center">{res.prposArea2Nm}</TableCell>
              <TableCell className="text-center">{res.ladUseSittnNm}</TableCell>
              <TableCell className="text-center">
                {res.tpgrphHgCodeNm}
              </TableCell>
              <TableCell className="text-center">
                {res.tpgrphFrmCodeNm}
              </TableCell>
              <TableCell className="text-center">
                {res.roadSideCodeNm}
              </TableCell>
              <TableCell className="text-right">{res.pblntfPclnd}</TableCell>
            </TableRow>
          ))
        ) : (
          // If there is no result:
          <TableRow>
            <TableCell colSpan={8} className="text-center">
              No Data
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
}
