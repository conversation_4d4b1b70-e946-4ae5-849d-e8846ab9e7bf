/**
 * 인증된 API 요청을 위한 Fetch Wrapper
 * 
 * OAuth 토큰 패스쓰루 방식을 사용하여 업스트림 API에 요청을 전달합니다.
 * X-Auth-Type: token 헤더와 Bearer 토큰을 자동으로 설정합니다.
 */

/**
 * JWT 토큰에서 userId(subject) 추출
 */
export function extractUserIdFromToken(authToken: string): string | undefined {
  try {
    // Bearer 접두사 제거
    const token = authToken.replace('Bearer ', '');

    // JWT 토큰의 payload 부분 디코딩
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.warn('[extractUserIdFromToken] 유효하지 않은 JWT 토큰 형식');
      return undefined;
    }

    const payload = JSON.parse(atob(parts[1]));
    const userId = payload.sub || payload.userId || payload.user_id;

    console.log('[extractUserIdFromToken] 추출된 userId:', userId);
    return userId;
  } catch (error) {
    console.error('[extractUserIdFromToken] JWT 토큰 파싱 오류:', error);
    return undefined;
  }
}

/**
 * MCP extra 객체에서 Authorization 토큰 추출
 */
export function extractAuthToken(extra: any): string | undefined {
  const debugMode = process.env.MCP_DEBUG === 'true';

  if (debugMode) {
    console.log('[extractAuthToken] Extra 객체 구조 분석:', JSON.stringify(extra, null, 2));
  }

  // MCP CallToolRequest의 extra 객체에서 토큰 추출
  // 실제 MCP 구조에 맞춰 다양한 가능한 위치에서 토큰을 찾아봄

  // 1. requestInfo.headers.authorization (실제 위치)
  if (extra?.requestInfo?.headers?.authorization) {
    console.log('[extractAuthToken] 토큰 발견: requestInfo.headers.authorization');
    return extra.requestInfo.headers.authorization;
  }

  if (extra?.requestInfo?.headers?.Authorization) {
    console.log('[extractAuthToken] 토큰 발견: requestInfo.headers.Authorization');
    return extra.requestInfo.headers.Authorization;
  }

  // 2. 직접 headers 객체
  if (extra?.headers?.authorization) {
    console.log('[extractAuthToken] 토큰 발견: headers.authorization');
    return extra.headers.authorization;
  }

  if (extra?.headers?.Authorization) {
    console.log('[extractAuthToken] 토큰 발견: headers.Authorization');
    return extra.headers.Authorization;
  }

  // 3. 최상위 authorization
  if (extra?.authorization) {
    console.log('[extractAuthToken] 토큰 발견: authorization');
    return extra.authorization;
  }

  // 4. MCP 클라이언트에서 전달되는 다른 가능한 형태들
  if (extra?.auth?.token) {
    console.log('[extractAuthToken] 토큰 발견: auth.token');
    return `Bearer ${extra.auth.token}`;
  }

  if (extra?.token) {
    console.log('[extractAuthToken] 토큰 발견: token');
    return `Bearer ${extra.token}`;
  }

  console.log('[extractAuthToken] ❌ 토큰을 찾을 수 없음');
  return undefined;
}



/**
 * 인증된 API 요청을 위한 Fetch Wrapper
 */
export async function authenticatedFetch(
  url: string,
  authToken: string,
  options: RequestInit = {}
): Promise<Response> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Auth-Type': 'token', // 게이트웨이에서 요구하는 헤더(필요 시)
    ...((options.headers as Record<string, string>) || {}),
  };

  // OAuth 토큰 패스쓰루 금지: 다운스트림에 클라이언트 토큰을 전달하지 않음
  // 서비스 토큰(B 방식)으로 교체
  const { getServiceAuthHeaders } = await import('./service-auth.js');
  const serviceAuth = await getServiceAuthHeaders(authToken);
  Object.assign(headers, serviceAuth.headers);

  console.log(`[Auth Fetch] 요청 URL: ${url}`);

  const controller = new AbortController();
  const timeoutMs = Number(process.env.GEON_FETCH_TIMEOUT_MS || 15000);
  const to = setTimeout(() => controller.abort(), timeoutMs);

  try {
    const response = await fetch(url, {
      ...options,
      headers,
      signal: controller.signal,
    });

    console.log(`[Auth Fetch] 응답 상태: ${response.status}, ${response.body}`);
    return response;
  } catch (err: any) {
    console.error(`[Auth Fetch] 요청 실패: ${err?.name || ''} ${err?.message || err}`);
    throw err;
  } finally {
    clearTimeout(to);
  }
}

/**
 * 인증이 필요하지 않은 API 요청을 위한 Fetch Wrapper
 */
export async function publicFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...((options.headers as Record<string, string>) || {}),
  };

  console.log(`[Public Fetch] 요청 URL: ${url}`);

  const response = await fetch(url, {
    ...options,
    headers,
  });

  console.log(`[Public Fetch] 응답 상태: ${response.status}`);

  return response;
}

/**
 * 도구 인증 요구사항에 따라 적절한 fetch 함수 선택
 */
export async function smartFetch(
  url: string,
  requiresAuth: boolean,
  authToken?: string,
  options: RequestInit = {}
): Promise<Response> {
  if (requiresAuth) {
    if (!authToken) {
      throw new Error('Authorization token is required for this operation');
    }
    return authenticatedFetch(url, authToken, options);
  } else {
    return publicFetch(url, options);
  }
}
