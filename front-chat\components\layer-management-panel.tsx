"use client";

import React, { useState, useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Layers3, 
  Eye, 
  EyeOff, 
  Palette, 
  Filter, 
  Trash2,
  Settings,
  Info,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { useLayerManager } from "@/hooks/use-layer-configs";
import { LayerStyleEditor } from "@/components/map/layer-style-editor";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import type { ManagedLayerProps } from "@/types/layer-manager";

interface LayerManagementPanelProps {
  className?: string;
}

interface LayerItemProps {
  layer: ManagedLayerProps;
  onToggleVisibility: (id: string) => void;
  onRemoveLayer: (id: string) => void;
  onUpdateStyle: (id: string, style: any) => void;
}

const LayerItem: React.FC<LayerItemProps> = ({
  layer,
  onToggleVisibility,
  onRemoveLayer,
  onUpdateStyle
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const layerManager = useLayerManager();
  const layerWithState = layerManager.getLayerWithState(layer.id);

  const getLayerTypeIcon = (type: string) => {
    switch (type) {
      case 'geoserver':
        return '🗺️';
      case 'geojson':
        return '📍';
      case 'wms':
        return '🌐';
      case 'wfs':
        return '📊';
      default:
        return '📄';
    }
  };

  const getServiceBadgeColor = (service?: string) => {
    switch (service) {
      case 'wms':
        return 'bg-blue-100 text-blue-800';
      case 'wfs':
        return 'bg-green-100 text-green-800';
      case 'geojson':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="mb-3">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <span className="text-lg">{getLayerTypeIcon(layer.type)}</span>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-sm font-medium truncate">
                {layer.name || layer.id}
              </CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge 
                  variant="secondary" 
                  className={cn("text-xs", getServiceBadgeColor(layer.service))}
                >
                  {layer.service?.toUpperCase() || layer.type.toUpperCase()}
                </Badge>
                {layer.source === 'tool-result' && (
                  <Badge variant="outline" className="text-xs">
                    AI 생성
                  </Badge>
                )}
                {layer.userModified && (
                  <Badge variant="outline" className="text-xs border-orange-200 text-orange-700">
                    수정됨
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onToggleVisibility(layer.id)}
              className="h-8 w-8 p-0"
            >
              {layer.visible ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4 text-gray-400" />
              )}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 w-8 p-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleContent>
          <CardContent className="pt-0">
            <Tabs defaultValue="info" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="info" className="text-xs">
                  <Info className="h-3 w-3 mr-1" />
                  정보
                </TabsTrigger>
                <TabsTrigger 
                  value="style" 
                  className="text-xs"
                  disabled={!layerWithState?.isStyleEditable}
                >
                  <Palette className="h-3 w-3 mr-1" />
                  스타일
                </TabsTrigger>
                <TabsTrigger value="actions" className="text-xs">
                  <Settings className="h-3 w-3 mr-1" />
                  작업
                </TabsTrigger>
              </TabsList>

              <TabsContent value="info" className="mt-4 space-y-3">
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <span className="font-medium text-gray-600">타입:</span>
                    <p className="mt-1">{layer.type}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">서비스:</span>
                    <p className="mt-1">{layer.service || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">지오메트리:</span>
                    <p className="mt-1">{layerWithState?.geometryType || 'N/A'}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600">Z-Index:</span>
                    <p className="mt-1">{layer.zIndex || 0}</p>
                  </div>
                </div>
                
                {layer.filter && (
                  <div>
                    <span className="font-medium text-gray-600 text-xs">필터:</span>
                    <p className="mt-1 text-xs bg-gray-50 p-2 rounded font-mono">
                      {layer.filter}
                    </p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="style" className="mt-4">
                {layerWithState?.isStyleEditable ? (
                  <LayerStyleEditor
                    layer={layer as any}
                    onStyleChange={(style) => onUpdateStyle(layer.id, style)}
                  />
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Palette className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">이 레이어는 스타일 편집을 지원하지 않습니다</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="actions" className="mt-4 space-y-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onRemoveLayer(layer.id)}
                  className="w-full text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  레이어 제거
                </Button>
              </TabsContent>
            </Tabs>
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export const LayerManagementPanel: React.FC<LayerManagementPanelProps> = ({
  className
}) => {
  const layerManager = useLayerManager();
  const { layers } = layerManager;

  // 레이어를 zIndex 기준으로 정렬 (높은 zIndex가 위에)
  const sortedLayers = useMemo(() => {
    return [...layers].sort((a, b) => (b.zIndex || 0) - (a.zIndex || 0));
  }, [layers]);

  // 스타일 편집 가능한 레이어 개수
  const styleableLayers = layerManager.getStyleableLayers();

  return (
    <div className={cn("w-full", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers3 className="h-5 w-5" />
            레이어 관리
            <Badge variant="secondary" className="ml-auto">
              {layers.length}개
            </Badge>
          </CardTitle>
          {styleableLayers.length > 0 && (
            <p className="text-sm text-gray-600">
              {styleableLayers.length}개 레이어에서 스타일 편집 가능
            </p>
          )}
        </CardHeader>
        
        <CardContent>
          {layers.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Layers3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-sm">추가된 레이어가 없습니다</p>
              <p className="text-xs mt-1">AI 채팅을 통해 레이어를 추가해보세요</p>
            </div>
          ) : (
            <div className="space-y-3">
              {sortedLayers.map((layer) => (
                <LayerItem
                  key={layer.id}
                  layer={layer}
                  onToggleVisibility={layerManager.toggleVisibility}
                  onRemoveLayer={layerManager.removeLayer}
                  onUpdateStyle={layerManager.updateStyle}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
