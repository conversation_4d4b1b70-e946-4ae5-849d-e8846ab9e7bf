import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 선택지 제공 도구 (Human-in-the-Loop)
 * AI SDK 5 패턴에 따라 execute 함수 없이 구현
 * 프론트엔드에서 사용자 승인을 받은 후 결과를 처리합니다.
 */
export const chooseOptionTool: AISDKToolWrapper = {
  description: `사용자에게 여러 선택지 중 하나를 고르도록 요청합니다. 

기능:
- 여러 옵션 중 하나를 선택할 수 있는 UI 제공
- Human-in-the-Loop 패턴으로 사용자 승인 필요
- 결과는 options 배열의 값 중 하나입니다

사용 예시:
- {"message": "배경지도를 선택하세요", "options": ["일반지도|eMapBasic", "항공지도|eMapAIR", "색각지도|eMapColor"]}
- {"message": "다음 중 검색할 지역을 선택하세요", "options": ["서울|seoul", "부산|busan", "대구|daegu"]}

옵션 형식:
- "표시될 텍스트|실제값" 형태로 제공
- 예: "일반지도|eMapBasic"`,

  parameters: z.object({
    message: z.string().describe("사용자에게 보여줄 안내 문구"),
    options: z
      .array(
        z.string().describe("사용자에게 표시될 텍스트. 반드시 'key|value' 형태로 제공하세요.")
      )
      .describe("사용자가 선택할 수 있는 옵션 문자열 배열"),
  }),

  // AI SDK 5 Human-in-the-Loop 패턴: execute 함수 제거
  // 프론트엔드에서 사용자 선택을 받은 후 결과 처리

  experimental_toToolResultContent: (result: any) => {
    // 사용자가 선택을 완료한 경우
    if (result.selectedOption) {
      return [
        {
          type: "text" as const,
          text: `선택 완료: ${result.selectedOption}`,
        },
      ];
    }

    // 사용자 선택 대기 중인 경우
    return [
      {
        type: "text" as const,
        text: result.message || "옵션을 선택해주세요.",
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const chooseOptionDefinition: MCPToolDefinition = createMCPTool(
  "chooseOption",
  chooseOptionTool,
  {
    title: "선택지 제공",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
    openWorldHint: true, // 사용자 입력에 따라 다양한 결과
  }
);