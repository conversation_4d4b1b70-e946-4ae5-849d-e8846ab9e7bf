import { createHash } from 'node:crypto';

/**
 * Service-to-Service 인증 헤더 제공 모듈
 *
 * 방식 B: RFC 8693 Token Exchange
 * - subject_token(클라이언트 토큰)을 RS에서 교환하여 다운스트림(Resource=GeOn API)용 액세스 토큰을 획득
 * - 다운스트림 호출에는 오직 교환된 Bearer 토큰만 사용 (클라이언트 토큰 패스스루 금지)
 */

export interface ServiceAuthHeaders {
  headers: Record<string, string>;
  source: 'service-token';
  expiresAt?: number; // epoch seconds
}

// 간단한 메모리 캐시 (key: hash(subject)+resource)
const tokenCache = new Map<string, { accessToken: string; expiresAt: number }>();

function sha256Base64(input: string): string {
  return Buffer.from(input).toString('base64'); // 단순 해시 대체(충분히 길어 충돌확률 낮음)
}

function getEnv(name: string, fallback?: string): string {
  const v = process.env[name] ?? fallback;
  if (!v) throw new Error(`ENV ${name} is required`);
  return v;
}

/**
 * subjectToken: 클라이언트로부터 받은 액세스 토큰(Authorization: Bearer ...)
 * resource: 다운스트림 리소스 식별자(RFC 8707) - 미지정 시 GEON_DOWNSTREAM_RESOURCE || GEON_API_BASE_URL 사용
 */
export async function getServiceAuthHeaders(subjectToken: string, resource?: string): Promise<ServiceAuthHeaders> {
  if (!subjectToken) throw new Error('subjectToken is required for token exchange');

  const issuer = getEnv('MAPPICK_ISSUER', 'https://login.geon.kr');
  const tokenEndpoint = `${issuer.replace(/\/$/, '')}/oauth2/token`;
  const clientId = getEnv('MAPPICK_CLIENT_ID');
  const clientSecret = getEnv('MAPPICK_CLIENT_SECRET');
  const targetResource = resource || process.env.GEON_DOWNSTREAM_RESOURCE || process.env.GEON_API_BASE_URL || '';
  if (!targetResource) throw new Error('GEON_DOWNSTREAM_RESOURCE or GEON_API_BASE_URL must be set');

  // 캐시 키: subjectToken 해시 + resource
  const subjectHash = sha256Base64(subjectToken);
  const cacheKey = `${subjectHash}::${targetResource}`;
  const cached = tokenCache.get(cacheKey);
  const now = Math.floor(Date.now() / 1000);
  if (cached && cached.expiresAt - 60 > now) {
    return { headers: { Authorization: `Bearer ${cached.accessToken}` }, source: 'service-token', expiresAt: cached.expiresAt };
  }

  // RFC 8693 Token Exchange (resource 파라미터 사용)
  const body = new URLSearchParams({
    grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',
    subject_token: subjectToken.startsWith('Bearer ') ? subjectToken.slice(7) : subjectToken,
    subject_token_type: 'urn:ietf:params:oauth:token-type:access_token',
    resource: targetResource,
    // 요청 범위가 필요하면 추가: scope: '...' (현재는 생략)
  });

  const basic = Buffer.from(`${clientId}:${clientSecret}`).toString('base64');
  const resp = await fetch(tokenEndpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${basic}`,
    },
    body,
  });

  if (!resp.ok) {
    const txt = await resp.text();
    throw new Error(`Token exchange failed: ${resp.status} ${resp.statusText} - ${txt}`);
  }

  const data = await resp.json();
  const accessToken: string = data.access_token;
  const expiresIn: number = data.expires_in ?? 300; // 기본 5분
  const expiresAt = now + expiresIn;

  tokenCache.set(cacheKey, { accessToken, expiresAt });

  return { headers: { Authorization: `Bearer ${accessToken}` }, source: 'service-token', expiresAt };
}

