import type { OAuthConfig, OAuthUserConfig } from "next-auth/providers"

// Mappick 사용자 프로필 타입 정의
export interface MappickProfile extends Record<string, any> {
  sub: string          // OAuth의 sub 필드 (사용자 ID)
  email: string        // 사용자 이메일
  email_verified?: boolean
  name?: string        // 사용자 이름
  profile?: {
    user_nm?: string
    user_image?: string
    user_se_code?: string
    email?: string
  }
  institution?: {
    instt_code?: string
    instt_nm?: string
    instt_url?: string
    instt_image?: string
  }
}

/**
 * Mappick OAuth 로그인을 추가합니다.
 *
 * ### 설정 방법
 *
 * #### 콜백 URL
 * ```
 * http://localhost:3000/api/auth/callback/mappick
 * ```
 *
 * #### 구성
 * ```ts
 * import NextAuth from "next-auth"
 * import Mappick from "@/lib/auth/providers/mappick"
 *
 * export default NextAuth({
 *   providers: [
 *     Mappick({
 *       clientId: process.env.MAPPICK_CLIENT_ID,
 *       clientSecret: process.env.MAPPICK_CLIENT_SECRET,
 *       issuer: process.env.MAPPICK_ISSUER
 *     }),
 *   ],
 * })
 * ```
 */
export default function Mappick<P extends MappickProfile>(
  options: OAuthUserConfig<P> & { issuer?: string }
): OAuthConfig<P> {
  const { issuer = "https://login.geon.kr" } = options
  
  return {
    id: "mappick",
    name: "Mappick",
    type: "oauth",
    authorization: {
      url: `${issuer}/oauth2/authorize`,
      params: {
        scope: "openid profile email institution",
        // MCP 리소스 서버를 위한 resource 파라미터 추가
        resource: process.env.NEXT_PUBLIC_MCP_SERVER_URL || "https://mcp.geon.kr"
      }
    },
    token: `${issuer}/oauth2/token`,
    userinfo: `${issuer}/userinfo`,
    profile(profile) {
      return {
        id: profile.sub, // 필수: 사용자의 고유 ID
        name: profile.profile?.user_nm ?? profile.email, // 이름: profile.user_nm 사용, 없으면 이메일
        email: profile.email, // 이메일: 최상위 email 사용
        image: null, // 이미지는 일단 null로 설정
        // 커스텀 필드 추가
        userSeCode: profile.profile?.user_se_code,
        institutionCode: profile.institution?.instt_code,
        institutionName: profile.institution?.instt_nm,
        institutionUrl: profile.institution?.instt_url,
      };
    },
    style: { 
      bg: "#4285F4", 
      text: "#fff",
      logo: "" // Mappick 로고 URL을 여기에 추가할 수 있습니다
    },
    options,
  }
}
