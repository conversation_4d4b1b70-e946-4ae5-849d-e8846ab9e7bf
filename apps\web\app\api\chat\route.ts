import {
	convertToModelMessages,
	streamText,
	type UIMessage,
	experimental_createMCPClient as createMCPClient,
	smoothStream,
} from "ai";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { resolveModel, type SupportedModelId } from "@/lib/ai/provider";

// Allow streaming responses up to 30 seconds
export const maxDuration = 30;

export async function POST(req: Request) {
	const {
		messages,
		modelId,
	}: { messages: UIMessage[]; modelId?: SupportedModelId } = await req.json();

	const model = await resolveModel(modelId);

	// MCP 클라이언트 생성 (StreamableHTTP 표준 사용)
	const mcpServerUrl = new URL(
		process.env.MCP_SERVER_URL || "http://localhost:3003/mcp",
	);
	const mcpClient = await createMCPClient({
		transport: new StreamableHTTPClientTransport(mcpServerUrl, {
			sessionId: `web-session-${Date.now()}`,
			requestInit: {
				headers: {
					"X-Auth-Type": "api-key",
					"X-API-Key":
						process.env.GEON_API_KEY || "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0",
				},
			},
		}),
	});

	// MCP 도구 가져오기
	const tools = await mcpClient.tools();

	const result = streamText({
		model,
		messages: convertToModelMessages(messages),
		tools,
		experimental_transform: smoothStream(),
		system: `You are a helpful GeOn spatial AI assistant that can help with:
- Address and location searches
- Coordinate lookups and reverse geocoding
- Layer information and spatial data queries
- Map-based analysis and visualization

Use the available MCP tools to provide accurate spatial information and help users with their geographic queries.`,
		onFinish: async () => {
			await mcpClient.close();
		},
	});

	return result.toUIMessageStreamResponse({
		sendSources: true,
		sendReasoning: true,
	});
}
