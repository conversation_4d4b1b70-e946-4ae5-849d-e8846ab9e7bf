import { NextRequest, NextResponse } from "next/server";
import { getLayer } from "@geon-ai/tools";
import { getApiConfig } from "@/lib/api-config";

export async function POST(request: NextRequest) {
  try {
    const { lyrId } = await request.json();

    if (!lyrId) {
      return NextResponse.json(
        { error: "레이어 ID가 필요합니다" },
        { status: 400 }
      );
    }

    // getLayer 도구 실행 (AI 대화와 동일한 파라미터 사용)
    const result = await getLayer.execute({
      userId: "admin",
      insttCode: "geonpaas",
      userSeCode: "14",
      lyrId: lyrId
    }, {
      abortSignal: new AbortController().signal,
      toolCallId: `api-layer-add-${lyrId}-${Date.now()}`,
      messages: []
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("=== 레이어 조회 실패 ===");
    console.error("Error:", error);
    console.error("Stack:", error instanceof Error ? error.stack : "No stack");

    return NextResponse.json(
      { error: "레이어 조회에 실패했습니다" },
      { status: 500 }
    );
  }
}
