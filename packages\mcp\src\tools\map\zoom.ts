import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import { MapSchemas } from '../shared/validators.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 지도 확대/축소 도구
 */
export const setMapZoomTool: AISDKToolWrapper = {
  description: `지도의 확대/축소 레벨을 조정합니다.

확대/축소 레벨:
- 1-5: 국가/대륙 레벨 (매우 넓은 범위)
- 6-10: 지역/도시 레벨 (넓은 범위)
- 11-15: 구/동 레벨 (중간 범위)
- 16-20: 건물/도로 레벨 (상세 범위)

사용 예시:
- "지도 확대해줘" → {"zoomDirection": "in", "zoomLevel": 2, "zoomType": "relative"}
- "더 넓게 보여줘" → {"zoomDirection": "out", "zoomLevel": 2, "zoomType": "relative"}
- "최대한 확대" → {"zoomLevel": 18, "zoomDirection": "in", "zoomType": "absolute"}
- "전체 보기" → {"zoomLevel": 2, "zoomDirection": "out", "zoomType": "absolute"}`,

  parameters: z.object({
    zoomLevel: z
      .number()
      .min(1)
      .max(20)
      .optional()
      .default(2)
      .describe("확대/축소 레벨 또는 변경량 (1-20)"),
    zoomType: MapSchemas.zoomType.optional().default("relative"),
    zoomDirection: MapSchemas.zoomDirection,
    description: z.string().describe("확대/축소에 대한 설명").optional(),
  }),

  execute: async ({ zoomLevel = 2, zoomType, zoomDirection, description }) => {
    try {
      return {
        success: true,
        zoom: zoomLevel,
        zoomType,
        zoomDirection,
        message:
          description ||
          `지도 ${zoomDirection === "in" ? "확대" : "축소"} 레벨이 ${
            zoomType === "relative" ? "변경" : "설정"
          }되었습니다.`,
      };
    } catch (error: any) {
      console.error("지도 확대/축소 실패:", error);
      return {
        success: false,
        error: `지도 확대/축소 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: result.message,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const setMapZoomDefinition: MCPToolDefinition = createMCPTool(
  "setMapZoom",
  setMapZoomTool,
  {
    title: "지도 확대/축소",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);