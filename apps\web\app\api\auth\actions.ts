"use server";

import { z } from "zod";

import { signIn } from "./auth";

const authFormSchema = z.object({
	id: z.string(),
	email: z.string().email().nullish(),
	password: z.string().min(6),
});

export interface LoginActionState {
	status: "idle" | "in_progress" | "success" | "failed" | "invalid_data";
}

export const login = async (
	_: LoginActionState,
	formData: FormData,
): Promise<LoginActionState> => {
	try {
		const validatedData = authFormSchema.parse({
			id: formData.get("id"),
			password: formData.get("password"),
		});
		
		const result = await signIn("credentials", {
			id: validatedData.id,
			password: validatedData.password,
			redirect: false, // 서버에서 리다이렉트하지 않음
		});

		// signIn 결과 확인
		if (result?.error) {
			return { status: "failed" };
		}

		return { status: "success" };
	} catch (error) {
		if (error instanceof z.ZodError) {
			return { status: "invalid_data" };
		}

		return { status: "failed" };
	}
};
