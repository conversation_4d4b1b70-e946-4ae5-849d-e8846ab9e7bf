/**
 * MCP 서버 설정 관리
 */

export interface ServerConfig {
  port: number;
  nodeEnv: string;
  filesystemRoot?: string;
  
  // 인증 설정
  disableAuth: boolean;
  authServerUrl: string;
  resourceServerUrl: string;
  allowedAudiences: [string, ...string[]];
  
  // API 설정 (MCP 서버 자체 인증용)
  apiKey?: string;
  apiBaseUrl: string;
  
  // 외부 API
  kakaoApiKey?: string;
  
  // 로깅 설정
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  enableRequestLogging: boolean;
  
  // 보안 설정
  allowedOrigins: string[];
  rateLimitPerMinute: number;
  
  // 개발 설정
  enableInspector: boolean;
  showDetailedErrors: boolean;
}

/**
 * 환경변수에서 설정을 로드하고 검증
 */
export function loadConfig(): ServerConfig {
  const config: ServerConfig = {
    port: parseInt(process.env.PORT || '3001', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    filesystemRoot: process.env.FILESYSTEM_ROOT,
    
    // 인증 설정
    disableAuth: process.env.DISABLE_AUTH === 'true',
    authServerUrl: process.env.GEON_AUTH_SERVER_URL || 'https://login.geon.kr',
    resourceServerUrl: process.env.GEON_RESOURCE_SERVER_URL || 'localhost:3001',
    allowedAudiences: (process.env.GEON_ALLOWED_AUDIENCES?.split(',') || [
      process.env.GEON_RESOURCE_SERVER_URL || 'localhost:3001',
      'console-client'
    ]) as [string, ...string[]],
    
    // API 설정 (MCP 서버 자체 인증용)
    apiKey: process.env.GEON_API_KEY,
    apiBaseUrl: process.env.GEON_API_BASE_URL || 'http://**************:14090',
    
    // 외부 API
    kakaoApiKey: process.env.KAKAO_REST_API_KEY,
    
    // 로깅 설정
    logLevel: (process.env.LOG_LEVEL as any) || 'info',
    enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING !== 'false',
    
    // 보안 설정
    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || ['*'],
    rateLimitPerMinute: parseInt(process.env.RATE_LIMIT_PER_MINUTE || '100', 10),
    
    // 개발 설정
    enableInspector: process.env.ENABLE_INSPECTOR === 'true',
    showDetailedErrors: process.env.SHOW_DETAILED_ERRORS !== 'false',
  };

  // 설정 검증
  validateConfig(config);
  
  return config;
}

/**
 * 설정 검증
 */
function validateConfig(config: ServerConfig): void {
  const errors: string[] = [];

  // 포트 검증
  if (isNaN(config.port) || config.port < 1 || config.port > 65535) {
    errors.push('PORT must be a valid port number (1-65535)');
  }

  // API 설정 검증 (MCP 서버 자체 인증용)
  if (!config.apiKey) {
    console.warn('⚠️  GEON_API_KEY is not set. Some tools may not work properly.');
  }

  // URL 검증
  try {
    new URL(config.authServerUrl);
  } catch {
    errors.push('GEON_AUTH_SERVER_URL must be a valid URL');
  }

  try {
    new URL(config.resourceServerUrl);
  } catch {
    errors.push('GEON_RESOURCE_SERVER_URL must be a valid URL');
  }

  try {
    new URL(config.apiBaseUrl);
  } catch {
    errors.push('GEON_API_BASE_URL must be a valid URL');
  }

  // 로그 레벨 검증
  const validLogLevels = ['debug', 'info', 'warn', 'error'];
  if (!validLogLevels.includes(config.logLevel)) {
    errors.push(`LOG_LEVEL must be one of: ${validLogLevels.join(', ')}`);
  }

  // 에러가 있으면 종료
  if (errors.length > 0) {
    console.error('❌ Configuration validation failed:');
    errors.forEach(error => console.error(`   - ${error}`));
    process.exit(1);
  }

  // 설정 요약 출력
  console.log('✅ Configuration loaded successfully:');
  console.log(`   - Port: ${config.port}`);
  console.log(`   - Environment: ${config.nodeEnv}`);
  console.log(`   - API Base URL: ${config.apiBaseUrl}`);
  console.log(`   - Auth Disabled: ${config.disableAuth}`);
  if (!config.disableAuth) {
    console.log(`   - Auth Server: ${config.authServerUrl}`);
  }
}

/**
 * 개발 환경 여부 확인
 */
export function isDevelopment(config: ServerConfig): boolean {
  return config.nodeEnv === 'development';
}

/**
 * 프로덕션 환경 여부 확인
 */
export function isProduction(config: ServerConfig): boolean {
  return config.nodeEnv === 'production';
}
