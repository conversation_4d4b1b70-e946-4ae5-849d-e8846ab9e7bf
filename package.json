{"name": "shadcn-ui-monorepo", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo run lint", "format": "biome format --write .", "check": "biome check .", "check:fix": "biome check --write .", "typecheck": "turbo run typecheck"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@workspace/typescript-config": "workspace:*", "turbo": "^2.5.5", "typescript": "5.7.3"}, "packageManager": "pnpm@10.4.1", "engines": {"node": ">=20"}}