declare module 'jwks-client' {
  interface JwksClientOptions {
    jwksUri: string;
    requestHeaders?: Record<string, string>;
    timeout?: number;
    cache?: boolean;
    rateLimit?: boolean;
    jwksRequestsPerMinute?: number;
    strictSsl?: boolean;
    getKeysInterceptor?: (keys: Record<string, any>) => Record<string, any>;
    cacheMaxEntries?: number;
    cacheMaxAge?: number;
  }

  interface SigningKey {
    getPublicKey(): string;
    rsaPublicKey?: string;
    publicKey?: string;
  }

  interface JwksClient {
    getSigningKey(kid: string, callback: (err: Error | null, key?: SigningKey) => void): void;
    getSigningKeys(callback: (err: Error | null, keys?: SigningKey[]) => void): void;
  }

  function jwksClient(options: JwksClientOptions): JwksClient;
  export = jwksClient;
}
