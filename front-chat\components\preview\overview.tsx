import { motion } from 'framer-motion';
import { BotIcon, CodeIcon, BuildingIcon } from 'lucide-react';
import { Card, CardContent } from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface OverviewProps {
  selectedModelId?: string;
}

// 모델별 설정 정의
const modelConfigs = {
  '지자체 공간정보 플랫폼 챗봇': {
    icon: BuildingIcon,
    title: '지자체 공간정보 플랫폼 챗봇',
    description: '플랫폼에 대해 궁금한 점을 여쭤보세요!',
    accordionTitle: '플랫폼 지원 범위',
    sections: [
      { title: "지원 데이터 종류" },
      { title: "플랫폼 목적 및 기능" },
      { title: "지도공간 서비스" },
      { title: "업무공간 서비스" }
    ],
    footerText: '지자체 공간정보 플랫폼에 대한 자세한 정보를 제공해드립니다.'
  },
  '지도개발 어시스턴트': {
    icon: CodeIcon,
    title: '지도개발 어시스턴트',
    description: '지도 개발과 관련된 질문을 해보세요',
    accordionTitle: '개발 지원 범위',
    sections: [
      { title: "지도 생성 방법" },
      { title: "레이어 생성" },
      { title: "이벤트 처리" },
      { title: "컨트롤/UI" }
    ],
    footerText: '코드 예제와 함께 자세한 설명을 제공해드립니다.'
  }
};

export const Overview = ({ selectedModelId = '지도개발 어시스턴트' }: OverviewProps) => {
  // 선택된 모델의 설정 가져오기
  const config = modelConfigs[selectedModelId as keyof typeof modelConfigs] || modelConfigs['지도개발 어시스턴트'];
  const IconComponent = config.icon;

  return (
    <motion.div
      key={`overview-${selectedModelId}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-sm backdrop-blur-sm mx-auto bg-gradient-to-b from-background/10 to-background/80 rounded-xl"
    >
      <Card className="border-none shadow-none bg-transparent">
        <CardContent className="p-6 space-y-6">
          <div className="relative">
            <motion.div
              className="flex items-center justify-center gap-4"
              initial={{ scale: 0.9 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
            >
              <IconComponent size={28} className="text-primary" />
              <span className="font-bold text-2xl">+</span>
              <BotIcon size={28} className="text-primary" />
            </motion.div>
            <motion.div
              className="text-center mt-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-lg font-semibold bg-clip-text bg-gradient-to-r from-primary to-primary/80">
                {config.title}
              </h2>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="space-y-4"
          >
            <p className="text-sm text-center">
              {config.description}
            </p>

            <Accordion type="single" collapsible defaultValue="examples" className="flex justify-center bg-background/40 rounded-lg">
              <AccordionItem value="examples" className="border-none">
                <AccordionTrigger className="justify-center gap-2 py-3 hover:no-underline">
                  <span className="text-sm font-medium">{config.accordionTitle}</span>
                </AccordionTrigger>
                <AccordionContent className="py-4">
                  <motion.div
                    className="space-y-6"
                    variants={{
                      hidden: { opacity: 0 },
                      show: {
                        opacity: 1,
                        transition: { staggerChildren: 0.1 }
                      }
                    }}
                    initial="hidden"
                    animate="show"
                  >
                    {config.sections.map((section, idx) => (
                      <motion.div
                        key={idx}
                        variants={{
                          hidden: { opacity: 0, y: 10 },
                          show: { opacity: 1, y: 0 }
                        }}
                        className="space-y-2"
                      >
                        <h3 className="text-sm font-medium text-primary">{section.title}</h3>
                      </motion.div>
                    ))}
                  </motion.div>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </motion.div>

          <motion.p
            className="text-xs text-center text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            {config.footerText}
          </motion.p>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default Overview;