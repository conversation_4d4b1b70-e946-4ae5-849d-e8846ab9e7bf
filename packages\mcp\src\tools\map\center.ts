import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 지도 중심점 이동 도구
 */
export const setMapCenterTool: AISDKToolWrapper = {
  description: `지도의 중심점을 특정 좌표로 이동합니다.

기능:
- 절대 좌표로 지도 중심점 이동
- 상대적 위치 이동 지원
- 이동에 대한 설명 제공

사용 예시:
- 특정 좌표로 이동: {"longitude": 127.027926, "latitude": 37.497175}
- 상대적 이동: {"longitude": 0.001, "latitude": 0.001, "moveType": "relative", "description": "동북쪽으로 이동"}`,

  parameters: z.object({
    longitude: z.number().min(-180).max(180).describe("경도 (X좌표)"),
    latitude: z.number().min(-90).max(90).describe("위도 (Y좌표)"),
    moveType: z
      .enum(["absolute", "relative"])
      .describe("이동 타입: absolute(절대좌표), relative(상대이동)")
      .optional()
      .default("absolute"),
    description: z.string().describe("이동에 대한 설명").optional(),
  }),

  execute: async ({ longitude, latitude, moveType, description }) => {
    try {
      return {
        success: true,
        center: [longitude, latitude],
        moveType,
        message:
          description ||
          `지도 중심점이 경도 ${longitude}, 위도 ${latitude}로 이동되었습니다.`,
      };
    } catch (error: any) {
      console.error("지도 중심점 이동 실패:", error);
      return {
        success: false,
        error: `지도 중심점 이동 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: result.message,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const setMapCenterDefinition: MCPToolDefinition = createMCPTool(
  "setMapCenter",
  setMapCenterTool,
  {
    title: "지도 중심점 이동",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);