import { z } from 'zod';

/**
 * 공통 검증 스키마
 */
export const CommonSchemas = {
  /** 좌표 스키마 */
  coordinate: z.object({
    longitude: z.number().min(-180).max(180).describe("경도"),
    latitude: z.number().min(-90).max(90).describe("위도"),
  }),

  /** 좌표 문자열 스키마 (127.111202,37.394912 형태) */
  coordinateString: z.string().regex(
    /^-?\d+(\.\d+)?,-?\d+(\.\d+)?(,name=.+)?$/,
    "좌표 형식: '경도,위도' 또는 '경도,위도,name=장소명'"
  ),

  /** 레이어 ID 스키마 */
  layerId: z.string().min(1).describe("레이어 ID"),

  /** 색상 스키마 (HEX) */
  hexColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, "HEX 색상 코드 (예: #FF0000)"),

  /** 투명도 스키마 */
  opacity: z.number().min(0).max(1).describe("투명도 (0.0-1.0)"),

  /** 확대/축소 레벨 스키마 */
  zoomLevel: z.number().min(1).max(20).describe("확대/축소 레벨 (1-20)"),

  /** 페이징 스키마 */
  pagination: z.object({
    pageIndex: z.string().optional().default("1").describe("페이지 번호"),
    pageSize: z.string().optional().default("10").describe("페이지당 결과 수"),
  }),

  /** 필터 조건 스키마 */
  filterCondition: z.object({
    attributeName: z.string().describe("속성명"),
    operator: z.enum(["=", ">", "<", ">=", "<=", "LIKE", "IN"]).describe("비교 연산자"),
    value: z.string().describe("조건 값"),
  }),

  /** 스타일 규칙 스키마 */
  styleRule: z.object({
    description: z.string().describe("규칙 설명"),
    color: z.string().regex(/^#[0-9A-Fa-f]{6}$/).describe("HEX 색상 코드"),
    conditions: z.array(z.object({
      attributeName: z.string().describe("속성명"),
      condition: z.enum(["like", "equal", "greater", "less", "greaterEqual", "lessEqual", "default"]).describe("조건 타입"),
      value: z.string().describe("조건 값"),
    })).describe("조건 배열"),
    logicalOperator: z.enum(["AND", "OR"]).describe("논리 연산자"),
  }),
};

/**
 * 지도 관련 스키마
 */
export const MapSchemas = {
  /** 방향 스키마 */
  direction: z.enum(["north", "south", "east", "west", "up", "down", "left", "right"]).describe("이동 방향"),

  /** 거리 스키마 */
  distance: z.string().regex(/^\d+(\.\d+)?\s*(m|km)?$/i, "거리 형식 (예: 500m, 1km, 2000)"),

  /** 배경지도 ID 스키마 */
  basemapId: z.enum(["eMapBasic", "eMapAIR", "eMapColor", "eMapWhite"]).describe("배경지도 ID"),

  /** 확대/축소 방향 스키마 */
  zoomDirection: z.enum(["in", "out"]).describe("확대/축소 방향"),

  /** 확대/축소 타입 스키마 */
  zoomType: z.enum(["absolute", "relative"]).describe("확대/축소 타입"),
};

/**
 * 레이어 관련 스키마
 */
export const LayerSchemas = {
  /** 레이어 타입 스키마 */
  layerType: z.enum(["1", "2", "3"]).describe("레이어 타입 (1: 점, 2: 선, 3: 면)"),

  /** 데이터 구분 스키마 */
  dataType: z.enum(["0", "1", "2", "9"]).describe("데이터 구분 (0: 전체, 1: 사용자, 2: 공유, 9: 국가)"),

  /** 심볼 타입 스키마 */
  symbolType: z.enum(["circle", "square", "triangle", "star", "cross", "x"]).describe("심볼 타입"),
};

/**
 * 주소 관련 스키마
 */
export const AddressSchemas = {
  /** 검색 키워드 스키마 */
  keyword: z.string().min(1).describe("검색할 주소나 장소명"),

  /** 좌표계 스키마 */
  srid: z.number().default(4326).describe("좌표계 (기본값: 4326)"),

  /** 경로 우선순위 스키마 */
  routePriority: z.enum(["RECOMMEND", "TIME", "DISTANCE"]).describe("경로 탐색 우선순위"),

  /** 회피 옵션 스키마 */
  avoidOption: z.enum(["ferries", "toll", "motorway", "schoolzone", "uturn"]).describe("회피 옵션"),

  /** 차종 스키마 */
  carType: z.number().default(1).describe("차종"),

  /** 유종 스키마 */
  fuelType: z.enum(["GASOLINE", "DIESEL", "LPG"]).describe("유종"),
};