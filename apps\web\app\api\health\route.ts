import { NextRequest, NextResponse } from "next/server";

const HEALTH_CHECK_URLS = {
  "Qwen3-4B": "http://121.163.19.104:8005/health",
  "Qwen3-14B": "http://121.163.19.104:8002/health",
} as const;

const TIMEOUT_MS = 5000;

async function ping(url: string, timeoutMs = TIMEOUT_MS): Promise<{ ok: boolean; ms: number }> {
  const start = Date.now();
  try {
    const controller = new AbortController();
    const t = setTimeout(() => controller.abort(), timeoutMs);
    const res = await fetch(url, { signal: controller.signal });
    clearTimeout(t);
    return { ok: res.ok, ms: Date.now() - start };
  } catch {
    return { ok: false, ms: Date.now() - start };
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const modelId = (searchParams.get("modelId") as keyof typeof HEALTH_CHECK_URLS) || "Qwen3-4B";
  const url = HEALTH_CHECK_URLS[modelId] || HEALTH_CHECK_URLS["Qwen3-4B"];
  const result = await ping(url);
  return NextResponse.json({ modelId, url, healthy: result.ok, latencyMs: result.ms });
}

