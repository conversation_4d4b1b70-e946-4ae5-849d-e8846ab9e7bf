"use client";

import { BASE_URL, createEstateClient, crtfckey } from "@geon-query/model";
import type { EstateClient } from "@geon-query/model/restapi/estate";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@geon-ui/react/primitives/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@geon-ui/react/primitives/tabs";
import React from "react";

import BuildingFloorTable from "../table/building-floor";
import LandBasicTable from "../table/land-basic";
import LandCharacteristicsTable from "../table/land-characteristics";
import LandHistoryTable from "../table/land-history";
import LandOwnershipTable from "../table/land-ownership";

function EstateWidget({
  children,
  ...props
}: React.ComponentProps<typeof Dialog>) {
  return <Dialog {...props}>{children}</Dialog>;
}

function EstateWidgetTrigger({
  className,
  ...props
}: React.ComponentProps<typeof Button>) {
  return (
    <DialogTrigger asChild>
      <Button
        className={cn("absolute bottom-2 left-58", className)}
        {...props}
      />
    </DialogTrigger>
  );
}

interface EstateWidgetContentProps {
  defaultValue?: keyof EstateClient;
  className?: string;
}

function EstateWidgetContent({
  defaultValue = "land",
  className,
}: EstateWidgetContentProps) {
  const client = createEstateClient({
    baseUrl: BASE_URL,
    crtfckey,
  });

  // TODO: pnu state handler
  const [pnu] = React.useState<string>("1111010100100010000");

  return (
    <DialogContent
      className={cn("w-full !max-w-[1000px] max-h-[660px]", className)}
    >
      <DialogHeader>
        <DialogTitle>Estate Widget</DialogTitle>
        <DialogDescription>Address Here</DialogDescription>
      </DialogHeader>
      <Tabs
        defaultValue={defaultValue}
        className="w-full h-[500px] overflow-hidden overflow-y-auto"
      >
        <TabsList>
          <TabsTrigger value="land">토지</TabsTrigger>
          <TabsTrigger value="building">건물</TabsTrigger>
          <TabsTrigger value="price">가격</TabsTrigger>
        </TabsList>
        <TabsContent
          value="land"
          className="flex flex-col gap-2 w-full h-[500px] overflow-hidden overflow-y-auto"
        >
          <div>
            <span>land/basic</span>
            <LandBasicTable
              pageNo={1}
              numOfRows={1}
              pnu={pnu}
              crtfckey={crtfckey}
              client={client}
            />
          </div>
          <div>
            <span>land/characteristics</span>
            <LandCharacteristicsTable
              pnu={pnu}
              crtfckey={crtfckey}
              numOfRows={10}
              pageNo={1}
              client={client}
            />
          </div>
          <div>
            <span>land/history</span>
            <LandHistoryTable
              pnu={pnu}
              crtfckey={crtfckey}
              numOfRows={10}
              pageNo={1}
              client={client}
            />
          </div>
          <div>
            <span>land/ownership</span>
            <LandOwnershipTable
              pnu={pnu}
              crtfckey={crtfckey}
              numOfRows={10}
              pageNo={1}
              client={client}
            />
          </div>
        </TabsContent>
        <TabsContent
          value="building"
          className="w-full h-[500px] overflow-hidden overflow-y-auto"
        >
          <BuildingFloorTable
            pnu={pnu}
            crtfckey={crtfckey}
            numOfRows={10}
            pageNo={1}
            client={client}
          />
        </TabsContent>

        <TabsContent
          value="price"
          className="w-full h-[500px] overflow-hidden overflow-y-auto"
        >
          price
        </TabsContent>
      </Tabs>
      <DialogFooter className="sm:justify-start">
        <DialogClose asChild>
          <Button type="button" variant="secondary">
            닫기
          </Button>
        </DialogClose>
      </DialogFooter>
    </DialogContent>
  );
}

export { EstateWidget, EstateWidgetContent, EstateWidgetTrigger };
