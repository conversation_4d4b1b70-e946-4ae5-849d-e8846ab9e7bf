{"$schema": "https://biomejs.dev/schemas/2.2.2/schema.json", "assist": {"actions": {"source": {"organizeImports": "on"}}, "enabled": true}, "formatter": {"enabled": true, "formatWithErrors": false, "attributePosition": "auto", "indentStyle": "tab", "indentWidth": 2, "lineWidth": 80, "lineEnding": "lf"}, "javascript": {"formatter": {"arrowParentheses": "always", "bracketSameLine": false, "bracketSpacing": true, "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "semicolons": "always", "trailingCommas": "all"}}, "json": {"formatter": {"trailingCommas": "none"}}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUndeclaredVariables": "warn", "useExhaustiveDependencies": "warn", "noInvalidPositionAtImportRule": "off"}, "suspicious": {"noExplicitAny": "warn", "noUnknownAtRules": "off"}, "style": {"useConst": "error"}}}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}}