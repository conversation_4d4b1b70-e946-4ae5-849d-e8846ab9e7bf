import Link from 'next/link';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: '로그인이 필요합니다.',
  description: '',
  robots: {
    index: false,
    follow: false,
  },
};

export default function UnauthorizedPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background">
      <div className="mx-auto max-w-md text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-muted-foreground">401</h1>
          <h2 className="mt-4 text-2xl font-semibold">로그인이 필요합니다.</h2>
          <p className="mt-2 text-muted-foreground">
            로그인 후 해당 페이지에 다시 접근하세요.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/geon-2d-map"
            className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
          >
            홈으로 돌아가기
          </Link>
          
          <div className="text-sm text-muted-foreground">
            <p>또는</p>
            <Link
              href="/login"
              className="text-primary hover:underline"
            >
              로그인 페이지로 이동
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

