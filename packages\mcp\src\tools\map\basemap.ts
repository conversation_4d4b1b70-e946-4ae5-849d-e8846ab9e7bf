import { z } from 'zod';
import { createMCPTool, createTextResult } from '../shared/utils.js';
import { MapSchemas } from '../shared/validators.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

// 베이스맵 목록 정의
const baseMap = {
  eMapBasic: "바로e맵 일반지도",
  eMapAIR: "바로e맵 항공지도", 
  eMapColor: "바로e맵 색각지도",
  eMapWhite: "바로e맵 백지도",
} as const;

/**
 * 배경지도 목록 조회 도구
 */
export const getBasemapListTool: AISDKToolWrapper = {
  description: `사용 가능한 배경지도 목록을 조회합니다.

반환되는 배경지도 목록:
- eMapBasic: 바로e맵 일반지도 (도로, 건물, 지형지물 포함)
- eMapAIR: 바로e맵 항공지도 (위성 항공사진)
- eMapColor: 바로e맵 색각지도 (색상 대비 강화)
- eMapWhite: 바로e맵 백지도 (깔끔한 흰색 배경)

사용 예시:
- {} (빈 객체로 호출)`,

  parameters: z.object({}),

  execute: async () => {
    try {
      return {
        success: true,
        basemaps: Object.entries(baseMap).map(([id, name]) => ({
          id,
          name,
          displayName: name,
        })),
      };
    } catch (error: any) {
      console.error("배경지도 목록 조회 실패:", error);
      return {
        success: false,
        error: `배경지도 목록 조회 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    const basemapList = result.basemaps
      .map((basemap: any, index: number) => 
        `${index + 1}. ${basemap.id}: ${basemap.displayName}`
      )
      .join("\n");

    return [
      {
        type: "text" as const,
        text: `사용 가능한 배경지도 목록:\n\n${basemapList}`,
      },
    ];
  },
};

/**
 * 배경지도 변경 도구
 */
export const changeBasemapTool: AISDKToolWrapper = {
  description: `배경지도를 변경합니다.

사용 가능한 배경지도 ID:
- eMapBasic: 일반지도 (기본 지도)
- eMapAIR: 항공지도 (위성지도) 
- eMapColor: 색각지도 (색상 대비)
- eMapWhite: 백지도 (심플한 배경)

키워드 매칭:
- "위성지도", "항공지도" → eMapAIR
- "일반지도", "기본지도" → eMapBasic
- "색각지도", "컬러지도" → eMapColor
- "백지도", "흰지도" → eMapWhite

사용 예시:
- {"basemapId": "eMapAIR"}
- {"basemapId": "eMapBasic"}`,

  parameters: z.object({
    basemapId: MapSchemas.basemapId,
  }),

  execute: async ({ basemapId }) => {
    try {
      const basemapName = baseMap[basemapId as keyof typeof baseMap];

      if (!basemapName) {
        return {
          success: false,
          error: `지원하지 않는 배경지도 ID입니다: ${basemapId}`,
          availableBasemaps: Object.keys(baseMap),
        };
      }

      return {
        success: true,
        basemap: basemapId,
        basemapId: basemapId,
        basemapName: basemapName,
        message: `배경지도가 ${basemapName}로 변경되었습니다.`,
      };
    } catch (error: any) {
      console.error("배경지도 변경 실패:", error);
      return {
        success: false,
        error: `배경지도 변경 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: result.message,
      },
    ];
  },
};

/**
 * MCP 도구 정의들
 */
export const getBasemapListDefinition: MCPToolDefinition = createMCPTool(
  "getBasemapList",
  getBasemapListTool,
  {
    title: "배경지도 목록 조회",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);

export const changeBasemapDefinition: MCPToolDefinition = createMCPTool(
  "changeBasemap", 
  changeBasemapTool,
  {
    title: "배경지도 변경",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);