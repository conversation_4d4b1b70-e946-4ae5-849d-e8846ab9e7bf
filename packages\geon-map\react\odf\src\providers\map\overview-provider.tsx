"use client";

import React, { useEffect } from "react";

import { CoreInstanceManager } from "../../stores/core-instances";
import { useMapStore } from "../../stores/map-store";

export function OverviewProvider({ children }: React.PropsWithChildren) {
  //const { map, odf } = useMapInstance();
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);
  const setOverviewInstance = useMapStore((state) => state.setOverviewInstance);

  useEffect(() => {
    // Map이 준비되면 Basemap Core 초기화
    try {
      if (map && odf && !isLoading) {
        const { overviewCore } = CoreInstanceManager.createOverviewCore(
          map,
          odf,
        );
        if (overviewCore !== null) setOverviewInstance(overviewCore);
        console.log("overviewCore", overviewCore);
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.error("❌ Failed to initialize overviewCore:", err);
    }
  });
  return <>{children}</>;
}
