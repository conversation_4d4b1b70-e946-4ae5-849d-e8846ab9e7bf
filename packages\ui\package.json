{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "biome lint .", "lint:fix": "biome lint --write .", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/react": "^2.0.23", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@radix-ui/react-use-controllable-state": "^1.2.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-popover": "^1.1.15", "ai": "^5.0.23", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-resizable-panels": "^3.0.5", "react-syntax-highlighter": "^15.6.6", "streamdown": "^1.1.3", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6", "use-stick-to-bottom": "^1.1.1", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@turbo/gen": "^2.5.5", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/react-syntax-highlighter": "^15.5.13", "@workspace/typescript-config": "workspace:*", "tailwindcss": "^4.1.11", "typescript": "^5.9.2"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts", "./ai-elements/*": "./src/components/ai-elements/*.tsx"}}