/**
 * OAuth 인증 미들웨어
 *
 * JWT 토큰 검증 및 OAuth 관련 기능을 담당합니다.
 */

import jwt, { Algorithm } from "jsonwebtoken";
import crypto from "crypto";

// (미사용 타입/함수 제거)

/**
 * OIDC Discovery로 jwks_uri 조회 (실패 시 /oauth2/jwks 폴백)
 */
async function discoverJwksUri(issuer: string): Promise<string> {
  const base = issuer.replace(/\/$/, '');
  const wellKnown = `${base}/.well-known/openid-configuration`;
  try {
    const resp = await fetch(wellKnown, { headers: { 'Accept': 'application/json' } });
    if (resp.ok) {
      const json = await resp.json();
      if (json.jwks_uri) {
        console.log('[JWT 검증] OIDC jwks_uri:', json.jwks_uri);
        return json.jwks_uri;
      }
    } else {
      console.warn('[JWT 검증] OIDC discovery 실패:', resp.status, resp.statusText);
    }
  } catch (e: any) {
    console.warn('[JWT 검증] OIDC discovery 예외:', e?.message || e);
  }
  const fallback = `${base}/oauth2/jwks`;
  console.log('[JWT 검증] jwks_uri 폴백 사용:', fallback);
  return fallback;
}

/**
 * JWKS에서 공개키 가져오기 (직접 fetch + 상세 로깅)
 */
async function getPublicKeyFromJwks(jwksUri: string, kid: string): Promise<string> {
  const resp = await fetch(jwksUri, { headers: { 'Accept': 'application/json' } });
  if (!resp.ok) {
    throw new Error(`JWKS fetch failed: ${resp.status} ${resp.statusText}`);
  }
  const body = await resp.json();
  const keys: any[] = body?.keys || [];
  console.log(`[JWT 검증] JWKS keys 수: ${keys.length}`);
  const kids = keys.map((k: any) => k.kid).filter(Boolean);
  console.log('[JWT 검증] JWKS kids:', kids);
  const jwk = keys.find((k: any) => k.kid === kid);
  if (!jwk) {
    throw new Error('The JWKS endpoint did not contain the signing key (kid=' + kid + ')');
  }
  const publicKeyObj = crypto.createPublicKey({ key: jwk, format: 'jwk' as any });
  const publicKeyPem = publicKeyObj.export({ type: 'spki', format: 'pem' }) as string;
  return publicKeyPem;
}

/**
 * JWT 토큰 검증
 */
export async function verifyJwtToken(token: string, config: any): Promise<any> {
  try {
    console.log(`[JWT 검증] 토큰 검증 시작: ${token.substring(0, 50)}...`);

    // JWT 디코딩
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded || typeof decoded === "string") {
      console.error("[JWT 검증] JWT 디코딩 실패");
      return null;
    }

    console.log("[JWT 검증] JWT 헤더:", decoded.header);
    console.log("[JWT 검증] JWT 페이로드:", decoded.payload);

    // kid 확인
    const kid = decoded.header.kid;
    if (!kid) {
      console.error("[JWT 검증] kid가 없습니다");
      return null;
    }

    // JWKS에서 공개키 가져오기
    const jwksUri = await discoverJwksUri(config.authServerUrl);
    console.log("[JWT 검증] JWKS URI:", jwksUri);

    const publicKey = await getPublicKeyFromJwks(jwksUri, kid);
    console.log("[JWT 검증] 공개키 가져오기 성공");

    // JWT 검증 옵션
    const verifyOptions = {
      issuer: config.authServerUrl.replace(/\/$/, ""),
      audience: config.allowedAudiences, // 허용된 aud 목록
      algorithms: ["RS256"] as Algorithm[],
      clockTolerance: 60,
    };

    console.log("[JWT 검증] 검증 옵션:", verifyOptions);
    console.log("[JWT 검증] 토큰 aud:", (decoded.payload as any).aud);

    // JWT 검증
    const payload = jwt.verify(token, publicKey, verifyOptions) as any;

    console.log("[JWT 검증] ✅ 토큰 검증 성공");
    console.log("[JWT 검증] 검증된 페이로드:", payload);

    return payload;
  } catch (error: any) {
    console.error("[JWT 검증] ❌ 토큰 검증 실패:", error.message);
    return null;
  }
}

/**
 * OAuth 미들웨어 설정
 */
export function setupOAuthMiddleware() {
  // 현재는 JWT 검증 함수만 export
  // 필요시 Express 미들웨어 함수들을 여기에 추가
  return {
    verifyJwtToken,
  };
}
