# GeOn MCP Server

GeOn 공간정보 API를 위한 Model Context Protocol (MCP) 서버입니다.

## 기능

### OAuth 기반 도구 (기본 활성화)
- `geon_get_layer_info_list`: 레이어 목록 조회
- `geon_get_layer_info_select`: 레이어 상세 정보 조회
- `geon_search_address`: 주소/위치 검색

### 특징
- **MCP 표준 준수**: Structured Content와 Output Schema 지원
- **OAuth 인증**: JWT 토큰 기반 자동 인증 처리
- **환경변수 기반 모드 설정**: OAuth/Legacy 모드 선택 가능
- **타입 안전성**: TypeScript와 Zod 스키마 검증

## 설치 및 실행

```bash
# 의존성 설치
pnpm install

# 빌드
pnpm build

# 서버 실행
pnpm start
```

## 환경변수 설정

`.env` 파일을 생성하고 다음 변수들을 설정하세요:

### MCP 서버 모드
```env
# OAuth 모드 활성화 (기본값: true)
MCP_OAUTH_ENABLED=true

# Legacy 모드 활성화 (기본값: false)  
MCP_LEGACY_ENABLED=false

# 디버그 모드
MCP_DEBUG=false
```

### API 설정
```env
# GeOn API 서버 URL
GEON_API_BASE_URL=http://**************:14090

# GeOn API 인증키 (OAuth 토큰이 없을 때 사용)
GEON_API_KEY=your-api-key-here
```

## 사용 방법

### Claude Desktop 설정

```json
{
  "mcpServers": {
    "geon-spatial": {
      "command": "node",
      "args": ["path/to/geon-mcp/dist/index.js"],
      "env": {
        "MCP_OAUTH_ENABLED": "true",
        "GEON_API_BASE_URL": "http://**************:14090"
      }
    }
  }
}
```

### AI SDK 사용

```typescript
import { experimental_createMCPClient as createMCPClient } from "ai";

const mcpClient = await createMCPClient({
  transport: {
    type: "http",
    url: "http://localhost:3001/mcp",
    headers: {
      'Authorization': 'Bearer your-oauth-token',
      'X-Auth-Type': 'token'
    }
  }
});

const tools = await mcpClient.tools();
```

## 도구 사용 예시

### 레이어 목록 조회
```json
{
  "name": "geon_get_layer_info_list",
  "arguments": {
    "layerName": "도로",
    "holdDataSeCode": "1"
  }
}
```

### 레이어 상세 정보 조회 (완전한 레이어 설정)
```json
{
  "name": "geon_get_layer_info_select",
  "arguments": {
    "lyrId": "LR0000003801"
  }
}
```

**응답 예시:**
```json
{
  "id": "LR0000003801",
  "name": "토지이용계획",
  "type": "geoserver",
  "visible": true,
  "zIndex": 0,
  "server": "http://**************:14090/map/api/map/wms",
  "layer": "workspace:CN0000004521",
  "service": "wms",
  "projection": "EPSG:5186",
  "geometryType": "polygon",
  "info": {
    "lyrId": "LR0000003801",
    "lyrNm": "토지이용계획",
    "description": "토지이용계획 정보를 제공하는 레이어",
    "metadata": {
      "cntntsId": "CN0000004521",
      "lyrClCode": "LC01",
      "lyrTySeCode": "3"
    }
  }
}
```

### 주소 검색
```json
{
  "name": "geon_search_address",
  "arguments": {
    "keyword": "서울시청",
    "countPerPage": 5
  }
}
```

## 개발

### 새 도구 추가

1. `src/tools/definitions.ts`에 도구 정의 추가
2. `src/handlers/oauth-tools.ts`에 핸들러 구현
3. 라우터에 도구 등록

### 테스트

```bash
# 단위 테스트
pnpm test

# 통합 테스트  
pnpm test:integration
```

## 라이선스

MIT
