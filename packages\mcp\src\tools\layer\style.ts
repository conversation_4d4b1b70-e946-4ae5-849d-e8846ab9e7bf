import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import { LayerSchemas, CommonSchemas } from '../shared/validators.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 스타일 규칙 생성을 위한 OpenAI 호출 (임시)
 * TODO: 실제 환경에서는 적절한 LLM 호출 로직으로 교체 필요
 */
async function generateStyleRulesWithLLM(
  userInstruction: string,
  attributes: string,
  layerName: string
): Promise<any> {
  // 임시로 간단한 규칙 생성 로직
  // 실제로는 OpenAI나 다른 LLM을 호출해야 함
  
  const defaultRules = [
    {
      description: "기본 스타일",
      color: "#0088FF",
      conditions: [],
      logicalOperator: "AND" as const,
    }
  ];

  // 사용자 요청에 따른 간단한 파싱 로직
  if (userInstruction.includes("빨간색") || userInstruction.includes("빨강")) {
    defaultRules[0].color = "#FF0000";
    defaultRules[0].description = "빨간색 스타일";
  } else if (userInstruction.includes("파란색") || userInstruction.includes("파랑")) {
    defaultRules[0].color = "#0000FF";
    defaultRules[0].description = "파란색 스타일";
  } else if (userInstruction.includes("초록색") || userInstruction.includes("녹색")) {
    defaultRules[0].color = "#00FF00";
    defaultRules[0].description = "초록색 스타일";
  }

  return { styleRules: defaultRules };
}

/**
 * 레이어 스타일 업데이트 도구
 */
export const updateLayerStyleTool: AISDKToolWrapper = {
  description: `레이어의 시각적 스타일을 변경합니다. 레이어 타입(점/선/면)에 따라 적절한 스타일이 적용됩니다.

지원하는 스타일 속성:
- color: 기본 색상 (hex 코드, 예: "#FF0000") - 모든 타입
- fillOpacity: 채우기 투명도 (0.0-1.0) - 점, 면 타입
- strokeColor: 윤곽선 색상 (hex 코드) - 모든 타입
- strokeWidth: 윤곽선 두께 (픽셀) - 모든 타입
- radius: 점 크기 (픽셀) - 점 타입만
- width: 선 두께 (픽셀) - 선 타입만
- symbol: 심볼 타입 (점 타입만) - "circle", "square", "triangle", "star", "cross", "x"

레이어 타입별 적용:
- 점(Point) 레이어: color, fillOpacity, strokeColor, strokeWidth, radius, symbol
- 선(Line) 레이어: color, strokeColor, strokeWidth, width
- 면(Polygon) 레이어: color, fillOpacity, strokeColor(윤곽선), strokeWidth(윤곽선)

사용 예시:
- "빨간색으로 바꿔줘" → {"layerId": "LR123", "color": "#FF0000"}
- "투명하게 해줘" → {"layerId": "LR123", "fillOpacity": 0.3}
- "윤곽선을 두껍게" → {"layerId": "LR123", "strokeWidth": 3}`,

  parameters: z.object({
    layerId: CommonSchemas.layerId,
    color: CommonSchemas.hexColor.optional(),
    fillOpacity: CommonSchemas.opacity.optional(),
    strokeColor: CommonSchemas.hexColor.optional(), 
    strokeWidth: z.number().min(0).max(20).optional().describe("윤곽선 두께 (픽셀)"),
    radius: z.number().min(1).max(50).optional().describe("점 크기 (픽셀, 점 타입만)"),
    width: z.number().min(1).max(20).optional().describe("선 두께 (픽셀, 선 타입만)"),
    symbol: LayerSchemas.symbolType.optional(),
    description: z.string().optional().describe("변경 사항에 대한 설명"),
  }),

  execute: async ({
    layerId,
    color,
    fillOpacity,
    strokeColor,
    strokeWidth,
    radius,
    width,
    symbol,
    description,
  }) => {
    try {
      // 스타일 객체 구성
      const styleUpdate: any = {};

      if (color) styleUpdate.color = color;
      if (fillOpacity !== undefined) styleUpdate.fillOpacity = fillOpacity;
      if (strokeColor) styleUpdate.strokeColor = strokeColor;
      if (strokeWidth !== undefined) styleUpdate.strokeWidth = strokeWidth;
      if (radius !== undefined) styleUpdate.radius = radius;
      if (width !== undefined) styleUpdate.width = width;
      if (symbol) styleUpdate.symbol = symbol;

      return {
        success: true,
        layerId,
        styleUpdate,
        description: description || "레이어 스타일이 업데이트되었습니다.",
      };
    } catch (error: any) {
      console.error("스타일 업데이트 실패:", error);
      return {
        success: false,
        error: `스타일 업데이트 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: result.description,
      },
    ];
  },
};

/**
 * 유형별 스타일 생성 도구
 */
export const generateCategoricalStyleTool: AISDKToolWrapper = {
  description: `레이어에 여러 조건별 스타일을 적용합니다. 사용자가 "A는 빨간색, B는 파란색" 같은 요청을 할 때 사용합니다.

기능:
- 자연어 스타일링 요청을 분석하여 조건별 스타일 규칙 생성
- 복수 조건 지원 (AND, OR 논리 연산)
- 자동 기본 스타일 추가

중요 사항:
- attributes 매개변수로 사용 가능한 속성 정보를 반드시 제공해야 합니다
- 속성 정보는 'c1(주소), c2(건물명)' 형태로 제공하세요

사용 예시:
- {"layerId": "LR123", "lyrNm": "건물", "attributes": "c1(주소), c2(건물명)", "userInstruction": "강남구는 빨간색, 서초구는 파란색으로"}
- {"layerId": "LR456", "lyrNm": "도로", "attributes": "a1(도로타입), a2(등급)", "userInstruction": "고속도로는 빨간색, 일반도로는 회색으로"}`,

  parameters: z.object({
    layerId: CommonSchemas.layerId,
    lyrNm: z.string().describe("레이어 이름"),
    attributes: z
      .string()
      .describe("🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'c1(주소), c2(건물명)' - 절대 'c1'처럼 속성명만 제공하지 마세요"),
    userInstruction: z
      .string()
      .describe("사용자의 자연어 스타일링 요청 (예: '용산구는 노란색, 강남구는 파란색, 나머지는 회색으로')"),
  }),

  execute: async ({ layerId, lyrNm, attributes, userInstruction }) => {
    try {
      // LLM을 사용하여 스타일 규칙 생성 (임시 구현)
      const processedStyleRules = await generateStyleRulesWithLLM(
        userInstruction,
        attributes,
        lyrNm
      );

      // logicalOperator가 누락된 경우 기본값 설정
      const normalizedStyleRules = processedStyleRules.styleRules.map(
        (rule: any) => ({
          ...rule,
          logicalOperator: rule.logicalOperator || "AND",
        })
      );

      // default 규칙이 없으면 자동으로 추가
      const hasDefaultRule = normalizedStyleRules.some(
        (rule: any) =>
          rule.conditions.length === 0 ||
          rule.conditions.some((cond: any) => cond.condition === "default")
      );
      
      let finalStyleRules = [...normalizedStyleRules];

      if (!hasDefaultRule) {
        finalStyleRules.push({
          description: "나머지 - 기본 스타일",
          color: "#808080", // 기본 회색
          conditions: [], // 빈 배열은 default 규칙을 의미
          logicalOperator: "AND" as const,
        });
      }

      return {
        success: true,
        layerId,
        attributes,
        styleRules: finalStyleRules,
        description: `${finalStyleRules.length}개 유형별 스타일 적용`,
        type: "categorical",
      };
    } catch (error: any) {
      console.error("유형별 스타일 생성 실패:", error);
      return {
        success: false,
        error: `유형별 스타일 생성 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    const styleCount = result.styleRules?.length || 0;
    return [
      {
        type: "text" as const,
        text: `${result.description} (${styleCount}개 스타일 규칙 생성)`,
      },
    ];
  },
};

/**
 * MCP 도구 정의들
 */
export const updateLayerStyleDefinition: MCPToolDefinition = createMCPTool(
  "updateLayerStyle",
  updateLayerStyleTool,
  {
    title: "레이어 스타일 업데이트",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);

export const generateCategoricalStyleDefinition: MCPToolDefinition = createMCPTool(
  "generateCategoricalStyle",
  generateCategoricalStyleTool,
  {
    title: "유형별 스타일 생성",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);