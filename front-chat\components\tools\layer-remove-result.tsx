"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { CompactResultTrigger } from "./common/compact-result-trigger";
import { getToolDisplayInfo } from "../annotations";

interface LayerRemoveResponse {
  layerId: string;
  description: string;
  success: boolean;
}

interface LayerRemoveResultProps {
  content: LayerRemoveResponse | string;
  className?: string;
}

export function LayerRemoveResult({ content, className }: LayerRemoveResultProps) {
  let result: LayerRemoveResponse;

  try {
    result = typeof content === 'string' ? JSON.parse(content) : content;
  } catch (e) {
    result = {
      layerId: '',
      description: 'Invalid content format',
      success: false
    };
  }

  const toolInfo = getToolDisplayInfo("removeLayer");

  if (!result.success) {
    return (
      <CompactResultTrigger
        icon={toolInfo.icon}
        title={toolInfo.label}
        state="partial-call"
        className={className}
        titleExtra={
          <Badge variant="outline" className="text-xs py-0 border bg-red-100/80 text-red-700 border-red-200/60">
            실패
          </Badge>
        }
      >
        <div className="text-xs text-red-700">
          {result.description || '레이어 삭제 중 오류가 발생했습니다.'}
        </div>
      </CompactResultTrigger>
    );
  }

  return (
    <CompactResultTrigger
      icon={toolInfo.icon}
      title={toolInfo.label}
      state="result"
      className={className}
      titleExtra={
        <Badge variant="outline" className="text-xs py-0 border bg-orange-100/80 text-orange-700 border-orange-200/60">
          삭제됨
        </Badge>
      }
    >
      <div className="space-y-2">
        <div className="text-xs text-neutral-600">
          {result.description}
        </div>

        <div className="text-xs text-neutral-500">
          삭제된 레이어 ID: {result.layerId}
        </div>
      </div>
    </CompactResultTrigger>
  );
}
