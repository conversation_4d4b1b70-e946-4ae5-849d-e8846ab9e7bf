import NextAuth, {User, Session, CredentialsSignin} from "next-auth";
import type { Provider } from "next-auth/providers";
import Credentials from "next-auth/providers/credentials";
import { authConfig } from "./auth.config";
import Mappick from "@/lib/auth/providers/mappick";

interface ExtendedSession extends Session {
    user: User;
}

interface ValidationResponse {
    code: number;
    message: string;
    result: {
        isValid: boolean;
        message: string;
    };
}

interface UserResponse {
    code: number;
    message: string;
    result: {
        userId: string;
        userNm: string;
        emailaddr: string | null;
        userSeCode: string;
        userSeCodeNm: string;
        userImage: string | null;
        insttCode: string;
        insttNm: string | null;
        insttUrl: string | null;
        message: string;
    };
}

class InvalidLoginError extends CredentialsSignin {
    code = "Invalid identifier or password"
}

const PRODUCTION = process.env.NODE_ENV === "production";
const API_CERTIFICATE_KEY = 'tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh'
const SMT_URL = 'https://gsapi.geon.kr/smt'

async function validateLogin(userId: string, password: string): Promise<ValidationResponse> {
    const params = new URLSearchParams({
        crtfckey: API_CERTIFICATE_KEY,
        userId,
        password
    });

    const response = await fetch(`${SMT_URL}/login/validation?${params.toString()}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'crtfckey': API_CERTIFICATE_KEY
        }
    });

    const data = await response.json();

    if (!response.ok) {
        throw new Error('로그인 검증에 실패했습니다.');
    }

    return data;
}

async function getUserInfo(userId: string): Promise<UserResponse> {
    const params = new URLSearchParams({
        crtfckey: API_CERTIFICATE_KEY,
        userId
    });

    const response = await fetch(`${SMT_URL}/users/id?${params.toString()}`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'crtfckey': API_CERTIFICATE_KEY
        }
    });

    const data = await response.json();

    if (!response.ok) {
        throw new Error('사용자 정보를 가져오는데 실패했습니다.');
    }

    return data;
}

export const providers: Provider[] = [
    Mappick({
        clientId: process.env.MAPPICK_CLIENT_ID!,
        clientSecret: process.env.MAPPICK_CLIENT_SECRET!,
        issuer: process.env.MAPPICK_ISSUER || "https://login.geon.kr",
    }),
    Credentials({
        credentials: {},
        async authorize({id, password}: any) {
            try {
                // admin 계정으로 프론트엔드 로그인 허용
                const frontendUserId = process.env.FRONTEND_LOGIN_USER_ID || 'admin';
                const frontendPassword = process.env.FRONTEND_LOGIN_PASSWORD || 'password1234';

                if (id === frontendUserId && password === frontendPassword) {
                    // admin 계정으로 로그인 성공
                    return {
                        id: frontendUserId,
                        name: 'GeOn City',
                        email: '@example.com',
                        userId: frontendUserId,
                        userNm: 'GeOn City',
                        emailaddr: '@example.com',
                        userSeCode: '14',
                        userSeCodeNm: '관리자',
                        userImage: null,
                        insttCode: 'GEON',
                        insttNm: 'GeOn',
                        insttUrl: null,
                        message: '로그인 성공'
                    };
                }

                // 기존 geonuser 계정도 유지 (호환성을 위해)
                if (id === 'geonuser') {
                    // 1. 로그인 검증
                    const validation = await validateLogin(id, password);

                    if (!validation.result.isValid) {
                        throw new CredentialsSignin(validation.result.message);
                    }

                    // 2. 유저 정보 조회
                    const userResponse = await getUserInfo(id);

                    if (userResponse.code !== 200) {
                        return new CredentialsSignin(userResponse.result.message);
                    }

                    // 3. 유저 정보 반환
                    return {
                        ...userResponse.result,
                        id: userResponse.result.userId,
                        name: userResponse.result.userNm || id,
                        email: userResponse.result.emailaddr || `${userResponse.result.userNm}`,
                    };
                }

                // 허용되지 않은 계정
                throw new CredentialsSignin('admin 또는 geonuser 계정으로만 로그인할 수 있습니다.');
            } catch (error) {
                console.error('Auth error:', error);
                throw error;
            }
        },
    })
]

export const providerMap = providers
  .map((provider) => {
      if (typeof provider === "function") {
          const providerData = provider()
          return { id: providerData.id, name: providerData.name }
      } else {
          return { id: provider.id, name: provider.name }
      }
  })
  .filter((provider) => provider.id !== "credentials")

const nextAuth = NextAuth({
    ...authConfig,
    providers,
    session: {
        strategy: "jwt",
        maxAge: 30 * 60, // 30분 (30분 * 60초)
    },
    callbacks: {
        async jwt({ token, user, account }) {
            if (user) {
                token.id = user.id;
            }
            // OAuth 토큰 저장
            if (account) {
                token.accessToken = account.access_token;
                token.refreshToken = account.refresh_token;
                token.provider = account.provider;
            }
            return token;
        },
        async session({session, token,}: {
            session: ExtendedSession;
            token: any;
        }) {
            if (session.user) {
                session.user.id = token.id as string;
            }
            // OAuth 토큰을 세션에 추가
            (session as any).accessToken = token.accessToken;
            (session as any).refreshToken = token.refreshToken;
            (session as any).provider = token.provider;
            return session;
        },
    }
});

export const handlers = nextAuth.handlers;
export const auth = nextAuth.auth as any;
export const signIn = nextAuth.signIn;
export const signOut = nextAuth.signOut;
