import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { createOpenAI } from "@ai-sdk/openai";

// Known VLLM-backed model configs (OpenAI-compatible)
const GEON_MODELS = {
  "Qwen3-4B": {
    apiIdentifier: "Qwen/Qwen3-4B",
    baseURL: "http://121.163.19.104:8005/v1",
    healthURL: "http://121.163.19.104:8005/health",
  },
  "Qwen3-14B": {
    apiIdentifier: "Qwen/Qwen2.5-14B",
    baseURL: "http://121.163.19.104:8002/v1",
    healthURL: "http://121.163.19.104:8002/health",
  },
} as const;

const OPENAI_FALLBACK_MODEL = "gpt-4.1-nano"; // Reasonable, cheap fallback

// Simple health check with timeout
async function isHealthy(url: string, timeoutMs = 5000): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), timeoutMs);
    const res = await fetch(url, { signal: controller.signal });
    clearTimeout(timeout);
    return res.ok;
  } catch {
    return false;
  }
}

export type SupportedModelId = keyof typeof GEON_MODELS | string;

type ResolvedModel = any;

// Returns a model instance resolved from requested modelId with health-based fallback
export async function resolveModel(modelId?: SupportedModelId): Promise<ResolvedModel> {
  // If a known GEON model is requested, prefer it and health-check
  if (modelId && modelId in GEON_MODELS) {
    const cfg = GEON_MODELS[modelId as keyof typeof GEON_MODELS];
    const healthy = await isHealthy(cfg.healthURL);

    if (healthy) {
      const geon = createOpenAICompatible({
        baseURL: cfg.baseURL,
        name: "geon",
        apiKey: "123"
      });
      return geon.chatModel(cfg.apiIdentifier);
    }
  }

  // Otherwise, try 4B as default if healthy
  const defaultCfg = GEON_MODELS["Qwen3-4B"];
  const defaultHealthy = await isHealthy(defaultCfg.healthURL);
  if (defaultHealthy) {
    const geon = createOpenAICompatible({
      baseURL: defaultCfg.baseURL,
      name: "geon",
      apiKey: "123"
    });
    return geon.chatModel(defaultCfg.apiIdentifier);
  }

  // Fallback to OpenAI
  const openai = createOpenAI({ apiKey: process.env.OPENAI_API_KEY || "" });
  return openai(OPENAI_FALLBACK_MODEL);
}

export function getHealthURL(modelId: SupportedModelId): string | undefined {
  if (modelId in GEON_MODELS) return GEON_MODELS[modelId as keyof typeof GEON_MODELS].healthURL;
  return undefined;
}

