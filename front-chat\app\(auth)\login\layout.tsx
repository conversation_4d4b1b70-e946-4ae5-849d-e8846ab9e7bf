import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "로그인",
  description: "말로 만드는 지도 demo 로그인",
  keywords: ["로그인", "GeOn", "지온파스", "인증"],
  openGraph: {
    title: "로그인",
    description: "말로 만드는 지도 demo 로그인.",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "로그인",
    description: "말로 만드는 지도 demo 로그인.",
  },
  robots: {
    index: false,
    follow: false,
  },
};

export default function LoginLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
