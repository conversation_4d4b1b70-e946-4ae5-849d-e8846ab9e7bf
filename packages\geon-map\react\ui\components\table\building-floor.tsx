"use client";

import type { APIRequestType, EstateClient } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { Skeleton } from "@geon-ui/react/primitives/skeleton";
import React from "react";

export default function BuildingFloorTable({
  pnu,
  crtfckey,
  client,
  numOfRows: nor,
  pageNo: pn,
  ...props
}: APIRequestType<EstateClient["building"]["floor"]> & {
  client: EstateClient;
}) {
  // Pagination States
  const [numOfRows] = React.useState<number>(nor);
  const [pageNo] = React.useState<number>(pn);

  const { data, isError, error, isLoading } = useAppQuery({
    queryKey: ["building/floor", pnu],
    queryFn: () =>
      client.building.floor({ pnu, crtfckey, numOfRows, pageNo, ...props }),
  });

  if (isLoading) return <Skeleton className="size-full" />;
  if (isError)
    return (
      <div className="flex justify-center align-middle">
        Error loading parcel data: {error as string}
      </div>
    );

  return (
    <div className="size-full text-clip">{JSON.stringify(data?.result)}</div>
  );
}
