"use client";

import React, { useEffect } from "react";

import { CoreInstanceManager } from "../../stores/core-instances";
import { useDrawStore } from "../../stores/draw-store";
import { useMapStore } from "../../stores/map-store";

/**
 * ClearProvider 설정 옵션
 */
export interface ClearProviderOptions {
  /** Clear Control 초기화 옵션 */
  clearOptions?: {
    clearAll?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 ClearProvider (Clear Control 전용)
 *
 * Clear Control만 초기화하는 독립적인 Provider입니다.
 * 지우기 기능이 필요한 경우에만 선언하세요.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ClearProvider clearOptions={{ clearAll: true }}>
 *     <ClearButton />
 *   </ClearProvider>
 * </MapProvider>
 * ```
 */
export function ClearProvider({
  children,
  clearOptions = { clearAll: true },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<ClearProviderOptions>) {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);
  const setClearCore = useDrawStore((state) => state.setClearCore);

  useEffect(() => {
    if (!autoInitialize) return;

    // Map이 준비되면 Clear Core 초기화
    if (map && odf && !isLoading) {
      try {
        const { clearCore, errors } = CoreInstanceManager.createClearOnlyCores(
          map,
          odf,
          clearOptions.clearAll,
        );

        if (clearCore) {
          setClearCore(clearCore);
        }

        if (errors.length > 0) {
          const error = new Error(
            `Clear Core 초기화 실패: ${errors.join(", ")}`,
          );
          console.error("❌ Clear Core initialization failed:", errors);
          onError?.(error);
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error("❌ Failed to initialize Clear core:", err);
        onError?.(err);
      }
    } else if (!map || !odf) {
      // Map이 초기화되지 않은 경우 경고 (로딩 중이 아닐 때만)
      if (!isLoading && process.env.NODE_ENV === "development") {
        console.warn(
          "⚠️ ClearProvider: Map 인스턴스가 준비되지 않았습니다.\n" +
            "확인사항: MapProvider가 ClearProvider보다 상위에 있는지 확인하세요.\n\n" +
            "올바른 구조:\n" +
            "<MapProvider>\n" +
            "  <ClearProvider>\n" +
            "    <App />\n" +
            "  </ClearProvider>\n" +
            "</MapProvider>",
        );
      }
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      setClearCore(null);
    };
  }, [isLoading]);

  return <>{children}</>;
}
