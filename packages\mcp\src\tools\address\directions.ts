import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import { AddressSchemas } from '../shared/validators.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 카카오 길찾기 API 응답 타입
 */
interface KakaoDirectionsResponse {
  trans_id: string;
  routes: Array<{
    result_code: number;
    result_msg: string;
    summary: {
      distance: number;
      duration: number;
      fare: {
        taxi: number;
        toll: number;
      };
    };
  }>;
  result_code: number;
  result_msg: string;
  summary: {
    distance: number;
    duration: number;
  };
  origin: {
    name?: string;
    x: number;
    y: number;
  };
  destination: {
    name?: string;
    x: number;
    y: number;
  };
  waypoints?: Array<{
    name?: string;
    x: number;
    y: number;
  }>;
  priority?: string;
  bound: {
    min_x: number;
    min_y: number;
    max_x: number;
    max_y: number;
  };
  fare?: {
    taxi: number;
    toll: number;
  };
  sections: Array<{
    distance: number;
    duration: number;
    roads: Array<{
      name: string;
      distance: number;
      duration: number;
      traffic_speed: number;
      traffic_state: number;
    }>;
  }>;
}

/**
 * 길찾기 도구
 */
export const searchDirectionsTool: AISDKToolWrapper = {
  description: `하나의 출발지에서 하나의 목적지까지의 경로에 대한 상세 정보를 제공합니다.

기능:
- 경로 탐색 및 상세 정보 제공
- 거리, 소요시간, 예상 요금 계산
- 경유지 지원 (최대 5개)
- 다양한 경로 탐색 옵션 제공

사용 예시:
- 기본 경로: {"origin": "127.111202,37.394912", "destination": "127.027926,37.497175"}
- 장소명 포함: {"origin": "127.111202,37.394912,name=판교역", "destination": "127.027926,37.497175,name=강남역"}
- 경유지 포함: {"origin": "127.111202,37.394912", "destination": "127.027926,37.497175", "waypoints": "127.123456,37.456789"}`,

  parameters: z.object({
    origin: z
      .string()
      .describe('출발지 좌표 (예: "127.111202,37.394912" 또는 "127.111202,37.394912,name=판교역")'),
    destination: z
      .string()
      .describe('목적지 좌표 (예: "127.111202,37.394912" 또는 "127.111202,37.394912,name=판교역")'),
    waypoints: z
      .string()
      .optional()
      .describe("경유지 좌표 (최대 5개, | 로 구분)"),
    priority: AddressSchemas.routePriority.optional().default("RECOMMEND"),
    avoid: AddressSchemas.avoidOption.optional().describe("회피 옵션"),
    roadevent: z
      .number()
      .optional()
      .default(0)
      .describe("도로 통제 정보 반영 (0: 전체, 1: 출발/도착지 제외, 2: 미반영)"),
    alternatives: z
      .boolean()
      .optional()
      .default(false)
      .describe("대안 경로 제공 여부"),
    road_details: z
      .boolean()
      .optional()
      .default(false)
      .describe("상세 도로 정보 제공 여부"),
    car_type: AddressSchemas.carType,
    car_fuel: AddressSchemas.fuelType.optional().default("GASOLINE"),
    car_hipass: z
      .boolean()
      .optional()
      .default(false)
      .describe("하이패스 장착 여부"),
    summary: z
      .boolean()
      .optional()
      .default(false)
      .describe("요약 정보 제공 여부"),
  }),

  execute: async (args) => {
    try {
      const apiKey = process.env.KAKAO_REST_API_KEY;
      if (!apiKey) {
        throw new Error("KAKAO_REST_API_KEY is not set");
      }

      const url = "https://apis-navi.kakaomobility.com/v1/directions";
      const params = new URLSearchParams({
        origin: args.origin,
        destination: args.destination,
        priority: args.priority,
        car_fuel: args.car_fuel,
        car_hipass: String(args.car_hipass),
        alternatives: String(args.alternatives),
        road_details: String(args.road_details),
      });

      // 선택적 파라미터 추가
      if (args.waypoints) params.append("waypoints", args.waypoints);
      if (args.avoid) params.append("avoid", args.avoid);
      if (args.roadevent) params.append("roadevent", String(args.roadevent));
      if (args.car_type) params.append("car_type", String(args.car_type));
      if (args.summary) params.append("summary", String(args.summary));

      const response = await fetch(`${url}?${params}`, {
        headers: {
          Authorization: `KakaoAK ${apiKey}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API 요청 실패: ${response.status} ${response.statusText}`);
      }

      const result = (await response.json()) as KakaoDirectionsResponse;
      return result;
    } catch (error: any) {
      console.error("길찾기 실행 실패:", error);
      return {
        error: `경로 검색 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    // 에러 응답 처리
    if (result.code === -2) {
      return [
        {
          type: "text" as const,
          text: `경로 탐색 실패: ${result.msg}`,
        },
      ];
    }

    // routes 배열에서 첫 번째 경로의 정보 추출
    if (!result.routes || !result.routes.length) {
      return [
        {
          type: "text" as const,
          text: "경로를 찾을 수 없습니다. 출발지와 목적지를 확인해주세요.",
        },
      ];
    }

    const route = result.routes[0];

    // result_code로 성공/실패 판단
    if (route.result_code !== 0) {
      return [
        {
          type: "text" as const,
          text: `경로 탐색 실패: ${route.result_msg || "알 수 없는 오류가 발생했습니다."}`,
        },
      ];
    }

    // 성공한 경우 핵심 정보만 추출
    const summary = route.summary;
    const distance = summary?.distance
      ? `${(summary.distance / 1000).toFixed(1)}km`
      : "정보 없음";
    const duration = summary?.duration
      ? `${Math.floor(summary.duration / 60)}분`
      : "정보 없음";
    const taxiFare = summary?.fare?.taxi
      ? `${summary.fare.taxi.toLocaleString()}원`
      : "정보 없음";
    const tollFare = summary?.fare?.toll
      ? `${summary.fare.toll.toLocaleString()}원`
      : "무료";

    // 출발지/목적지 정보
    const originName = result.origin?.name || "출발지";
    const destinationName = result.destination?.name || "목적지";

    const routeInfo = `경로 탐색이 완료되었습니다.

📍 ${originName} → ${destinationName}
🚗 거리: ${distance}
⏱️ 소요시간: ${duration}
💰 예상 택시요금: ${taxiFare}
🛣️ 통행료: ${tollFare}

경로가 지도에 자동으로 표시되었습니다.`;

    return [
      {
        type: "text" as const,
        text: routeInfo,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const searchDirectionsDefinition: MCPToolDefinition = createMCPTool(
  "searchDirections",
  searchDirectionsTool,
  {
    title: "길찾기",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);