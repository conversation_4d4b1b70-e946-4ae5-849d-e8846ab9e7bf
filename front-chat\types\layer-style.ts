// WMS/WFS 레이어 스타일 타입 정의

// 기본 필터 타입
export type FilterOperator = '==' | '!=' | '>' | '<' | '>=' | '<=' | '*=' | '&&' | '||';

export interface BasicFilter {
  operator: FilterOperator;
  column: string;
  value: string | number | null;
}

export interface LikeFilter {
  operator: '*=';
  column: string;
  value: string; // wildCard="*" singleChar="." escape="!"
}

export interface LogicalFilter {
  operator: '&&' | '||';
  conditions: [BasicFilter | LikeFilter, BasicFilter | LikeFilter, ...Array<BasicFilter | LikeFilter>];
}

export type StyleFilter = BasicFilter | LikeFilter | LogicalFilter;

// 스케일 범위 타입
export interface ScaleDenominator {
  min?: number;
  max?: number;
}

// 점(Point) 스타일 심볼라이저
export interface MarkSymbolizer {
  kind: 'Mark';
  wellKnownName: 'circle' | 'square' | 'triangle' | 'star' | 'cross' | 'x' | 'Square';
  radius: number;
  color: string; // rgba 값 입력시 자동변환
  fillOpacity: number; // 0~1
  strokeColor?: string; // rgba 값 입력시 자동변환
  strokeOpacity?: number; // 0~1
  strokeWidth?: number;
  offset?: [number, number]; // [x이동량, y이동량]
  offsetGeometry?: string; // offset 적용 시킬 geometry 타입 칼럼명
}

// 선(Line) 스타일 심볼라이저
export interface LineSymbolizer {
  kind: 'Line';
  color: string; // rgba 값 입력시 자동변환
  cap?: 'butt' | 'round' | 'square'; // 라인의 끝 표현 방식
  join?: 'mitre' | 'round' | 'bevel'; // 라인이 꺾이는 부분 표현 방식
  opacity: number; // 0~1
  width: number;
  dasharray?: number[]; // 대시 간격 조절
  dashOffset?: number; // 선의 시작점에서 얼마나 떨어진 곳에서부터 점선을 표시할지
}

// 면(Polygon) 스타일 심볼라이저
export interface FillSymbolizer {
  kind: 'Fill';
  color: string; // rgba 값 입력시 자동변환
  fillOpacity: number; // 0~1
  outlineColor?: string; // rgba 값 입력시 자동변환
  outlineWidth?: number;
  outlineOpacity?: number; // 0~1
  outlineDasharray?: number[]; // 윤곽선 대쉬 간격
}

// 심볼라이저 유니온 타입
export type Symbolizer = MarkSymbolizer | LineSymbolizer | FillSymbolizer;

// 스타일 룰
export interface StyleRule {
  name: string;
  scaleDenominator?: ScaleDenominator;
  filter?: StyleFilter;
  symbolizers: Symbolizer[];
}

// WMS 레이어 스타일 (SLD 방식)
export interface WMSLayerStyle {
  rules: StyleRule[];
}

// WFS 레이어 스타일 (Flat Style 방식)
export interface WFSLayerStyle {
  'stroke-color'?: string;
  'stroke-width'?: number;
  'stroke-opacity'?: number;
  'fill-color'?: string;
  'fill-opacity'?: number;
  'circle-radius'?: number;
  'circle-fill-color'?: string;
  'circle-fill-opacity'?: number;
  'circle-stroke-color'?: string;
  'circle-stroke-width'?: number;
  'circle-stroke-opacity'?: number;
}

// 레이어 타입별 스타일 유니온
export type LayerStyle = WMSLayerStyle | WFSLayerStyle;

// 레이어 렌더 옵션
export interface LayerRenderOptions {
  style: LayerStyle;
}

// 지오메트리 타입별 기본 스타일 생성 헬퍼
export const createDefaultPointStyle = (options?: Partial<MarkSymbolizer>): WMSLayerStyle => ({
  rules: [{
    name: 'Default Point Rule',
    symbolizers: [{
      kind: 'Mark',
      wellKnownName: 'circle',
      radius: 6,
      color: '#FF0000',
      fillOpacity: 0.8,
      strokeColor: '#000000',
      strokeWidth: 1,
      strokeOpacity: 1,
      ...options
    }]
  }]
});

export const createDefaultLineStyle = (options?: Partial<LineSymbolizer>): WMSLayerStyle => ({
  rules: [{
    name: 'Default Line Rule',
    symbolizers: [{
      kind: 'Line',
      color: '#0000FF',
      width: 2,
      opacity: 1,
      cap: 'round',
      join: 'round',
      ...options
    }]
  }]
});

export const createDefaultPolygonStyle = (options?: Partial<FillSymbolizer>): WMSLayerStyle => ({
  rules: [{
    name: 'Default Polygon Rule',
    symbolizers: [{
      
      kind: 'Fill',
      color: '#AAAAAA',
      fillOpacity: 0.5,
      outlineColor: '#000000',
      outlineWidth: 1,
      outlineOpacity: 1,
      ...options
    }]
  }]
});

// WFS 레이어용 기본 스타일 생성 헬퍼
export const createDefaultWFSPointStyle = (options?: Partial<WFSLayerStyle>): WFSLayerStyle => ({
  'circle-radius': 6,
  'circle-fill-color': '#FF0000',
  'circle-fill-opacity': 0.8,
  'circle-stroke-color': '#000000',
  'circle-stroke-width': 1,
  'circle-stroke-opacity': 1,
  ...options
});

export const createDefaultWFSLineStyle = (options?: Partial<WFSLayerStyle>): WFSLayerStyle => ({
  'stroke-color': '#0000FF',
  'stroke-width': 2,
  'stroke-opacity': 1,
  ...options
});

export const createDefaultWFSPolygonStyle = (options?: Partial<WFSLayerStyle>): WFSLayerStyle => ({
  'fill-color': '#00FF00',
  'fill-opacity': 0.5,
  'stroke-color': '#000000',
  'stroke-width': 1,
  'stroke-opacity': 1,
  ...options
});
