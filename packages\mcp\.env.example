# GeOn MCP Server 환경변수 설정 예제

# === MCP 서버 모드 설정 ===

# OAuth 모드 활성화 (기본값: true)
# OAuth 기반 도구들을 사용할지 여부
MCP_OAUTH_ENABLED=true

# Legacy 모드 활성화 (기본값: false)
# 기존 @geon-ai/tools 패키지의 도구들을 사용할지 여부
MCP_LEGACY_ENABLED=false

# MCP 디버그 모드
MCP_DEBUG=false

# === 서버 설정 ===
PORT=3001
NODE_ENV=development

# 파일시스템 루트 (선택사항)
FILESYSTEM_ROOT=/path/to/filesystem/root

# === 인증 설정 ===

# 인증 비활성화 (개발/테스트용)
DISABLE_AUTH=true

# OAuth 인증 서버 설정
GEON_AUTH_SERVER_URL=https://login.geon.kr
GEON_RESOURCE_SERVER_URL=https://mcp.geon.kr

# === API 설정 ===

# API 타입 설정 (token 또는 legacy)
GEON_API_TYPE=token

# 레거시 API 키 방식 (GEON_API_TYPE이 설정되지 않았을 때 사용)
GEON_API_KEY=your-legacy-api-key-here

# API 베이스 URL
GEON_API_BASE_URL=http://121.163.19.101:14090

# === 외부 API 설정 ===

# 카카오 API (길찾기 도구용)
KAKAO_REST_API_KEY=your-kakao-api-key-here

# === 로깅 설정 ===

# 로그 레벨 (debug, info, warn, error)
LOG_LEVEL=info

# 요청 로깅 활성화
ENABLE_REQUEST_LOGGING=true

# === 보안 설정 ===

# CORS 허용 도메인 (쉼표로 구분)
ALLOWED_ORIGINS=*

# 요청 제한 (분당 요청 수)
RATE_LIMIT_PER_MINUTE=100

# === 개발 설정 ===

# MCP Inspector 활성화
ENABLE_INSPECTOR=false

# 상세 에러 메시지 표시
SHOW_DETAILED_ERRORS=true
