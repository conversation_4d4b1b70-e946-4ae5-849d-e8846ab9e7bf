"use client";

import React, { useEffect } from "react";

import { CoreInstanceManager } from "../../stores/core-instances";
import { useMapStore } from "../../stores/map-store";

/**
 * BasemapProvider 설정 옵션
 */
export interface BasemapProviderOptions {
  /** Basemap Control 초기화 옵션 */
  basemapOptions?: {
    basemapList?: any;
    urls?: any;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 BasemapProvider (Basemap Control 전용)
 *
 * Basemap Control만 초기화하는 독립적인 Provider입니다.
 * 베이스맵 변경 기능이 필요한 경우에만 선언하세요.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <BasemapProvider basemapOptions={{ basemapList: [...] }}>
 *     <BasemapSwitcher />
 *   </BasemapProvider>
 * </MapProvider>
 * ```
 */
export function BasemapProvider({
  children,
  basemapOptions = {},
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<BasemapProviderOptions>) {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);
  const setBasemapInstance = useMapStore((state) => state.setBasemapInstance);

  useEffect(() => {
    if (!autoInitialize) return;

    // Map이 준비되면 Basemap Core 초기화
    if (map && odf && !isLoading) {
      try {
        const { basemapInstance, errors } =
          CoreInstanceManager.createBasemapCores(map, odf, basemapOptions);

        if (basemapInstance) {
          setBasemapInstance(basemapInstance);
        }

        if (errors.length > 0) {
          const error = new Error(
            `Basemap Core 초기화 실패: ${errors.join(", ")}`,
          );
          console.error("❌ Basemap Core initialization failed:", errors);
          onError?.(error);
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error("❌ Failed to initialize Basemap core:", err);
        onError?.(err);
      }
    } else if (!map || !odf) {
      // Map이 초기화되지 않은 경우 경고 (로딩 중이 아닐 때만)
      if (!isLoading && process.env.NODE_ENV === "development") {
        console.warn(
          "⚠️ BasemapProvider: Map 인스턴스가 준비되지 않았습니다.\n" +
            "확인사항: MapProvider가 BasemapProvider보다 상위에 있는지 확인하세요.\n\n" +
            "올바른 구조:\n" +
            "<MapProvider>\n" +
            "  <BasemapProvider>\n" +
            "    <App />\n" +
            "  </BasemapProvider>\n" +
            "</MapProvider>",
        );
      }
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      setBasemapInstance(null);
    };
  }, [isLoading]);

  return <>{children}</>;
}
