import { z } from "zod";

/**
 * MCP 도구 정의 인터페이스
 * MCP 공식 문서의 Tool definition structure를 따름
 */
export interface MCPToolDefinition {
	name: string;
	description: string;
	inputSchema: any; // JSON Schema
	outputSchema?: any; // JSON Schema for output validation (MCP 표준)
	requiresAuth?: boolean; // OAuth authentication requirement
	annotations?: {
		title?: string;
		readOnlyHint?: boolean;
		destructiveHint?: boolean;
		idempotentHint?: boolean;
		openWorldHint?: boolean;
	};
}

/**
 * 도구 실행 결과 타입
 */
export interface ToolResult {
	content: Array<{
		type: "text" | "image" | "resource";
		text?: string;
		data?: string;
		url?: string;
		mimeType?: string;
	}>;
	isError?: boolean;
}

/**
 * AI SDK 5 호환 도구 래퍼 인터페이스
 */
export interface AISDKToolWrapper {
	description: string;
	parameters: z.ZodSchema;
	execute?: (args: any) => Promise<any>;
	experimental_toToolResultContent?: (result: any) => Array<{
		type: "text" | "image" | "resource";
		text?: string;
		data?: string;
		url?: string;
		mimeType?: string;
	}>;
}

/**
 * 기본 응답 타입
 */
export interface BaseResponse<T = any> {
	success?: boolean;
	error?: string;
	result?: T;
	message?: string;
}

/**
 * 좌표 타입
 */
export interface Coordinate {
	longitude: number;
	latitude: number;
}

/**
 * 바운딩 박스 타입
 */
export interface BoundingBox {
	minX: number;
	minY: number;
	maxX: number;
	maxY: number;
}
