#!/usr/bin/env node

/**
 * GeOn MCP Server
 *
 * OAuth 기반 MCP 서버의 진입점
 * 최소한의 서버 실행 로직만 포함하며, 핵심 로직은 별도 모듈로 분리됨
 */

import 'dotenv/config';
import express from 'express';
import { randomUUID } from 'node:crypto';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import { loadConfig } from './config.js';
import { createMCPServer } from './server/mcp-server.js';
import { setupOAuthMiddleware } from './server/oauth-middleware.js';

// 설정 로드
const config = loadConfig();

// 인증서버 URL을 환경변수로 오버라이드 (개발용)
if (process.env.GEON_AUTH_SERVER_URL) {
  config.authServerUrl = process.env.GEON_AUTH_SERVER_URL;
}

// 세션별 transport 저장소 (stateful 모드용)
const transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

/**
 * Set the working directory to the root of the filesystem.
 */
if (config.filesystemRoot) {
  process.chdir(config.filesystemRoot);
}

// JWT 관련 함수들은 oauth-middleware.ts로 이동됨

/**
 * 메인 서버 실행 함수
 */
async function main() {
  const app = express();
  app.use(express.json());

  // CORS 설정 (Streamable HTTP에서 mcp-session-id 헤더 지원)
  app.use((req, res, next) => {
    const allowedOrigins = config.allowedOrigins.includes('*')
      ? '*'
      : config.allowedOrigins.join(',');

    res.header('Access-Control-Allow-Origin', allowedOrigins);
    res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Auth-Type, mcp-session-id');
    res.header('Access-Control-Expose-Headers', 'mcp-session-id');

    if (req.method === 'OPTIONS') {
      res.sendStatus(200);
    } else {
      next();
    }
  });

  // API 키 기반 인증 미들웨어 (/mcp 보호)
  const mcpAuth: express.RequestHandler = (req, res, next) => {
    if (config.disableAuth) {
      next();
      return;
    }

    // API 키 기반 인증 확인
    const apiKey = req.headers['x-api-key'] || req.headers['X-API-Key'];
    const authType = req.headers['x-auth-type'] || req.headers['X-Auth-Type'];

    // API 키 모드인 경우
    if (authType === 'api-key') {
      if (!apiKey) {
        res.status(401).json({ error: 'API key required' });
        return;
      }

      // API 키 검증 (간단한 검증 - 실제 환경에서는 더 강화된 검증 필요)
      const validApiKey = process.env.GEON_API_KEY || 'UxizIdSqCePz93ViFt8ghZFFJuOzvUp0';
      if (apiKey !== validApiKey) {
        res.status(401).json({ error: 'Invalid API key' });
        return;
      }

      // API 키 인증 성공
      (req as any).user = { type: 'api-key', apiKey };
      next();
      return;
    }

    // OAuth 토큰 기반 인증 (기존 로직 유지)
    const auth = req.headers['authorization'];
    if (!auth || !auth.toString().toLowerCase().startsWith('bearer ')) {
      res.setHeader('WWW-Authenticate',
        `Bearer realm="geon-mcp", scope="mcp:tools spatial:read"`);
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const token = auth.toString().slice(7);
    const { verifyJwtToken } = setupOAuthMiddleware();
    verifyJwtToken(token, config)
      .then((payload) => {
        if (!payload) {
          res.setHeader('WWW-Authenticate',
            `Bearer realm="geon-mcp", error="invalid_token", scope="mcp:tools spatial:read"`);
          res.status(401).json({ error: 'Invalid token' });
          return;
        }
        (req as any).user = { sub: payload.sub, scope: payload.scope, aud: payload.aud };
        next();
      })
      .catch(() => {
        res.setHeader('WWW-Authenticate',
          `Bearer realm="geon-mcp", error="invalid_token", scope="mcp:tools spatial:read"`);
        res.status(401).json({ error: 'Unauthorized' });
      });
  };
  app.use('/mcp', mcpAuth);

  // OAuth Protected Resource Metadata (RFC 9728)
  app.get('/.well-known/oauth-protected-resource', (_req, res) => {
    res.json({
      resource: config.resourceServerUrl,
      authorization_servers: [config.authServerUrl],
      scopes_supported: ['mcp:tools', 'spatial:read'],
      bearer_methods_supported: ['header'],
      resource_documentation: 'https://docs.geon.kr/mcp'
    });
  });

  // MCP 서버 정보 제공 엔드포인트 (선택사항)
  app.get('/.well-known/mcp-server', (_req, res) => {
    res.json({
      name: 'geon-mcp-server',
      version: '1.13.2',
      description: 'GeOn Spatial AI Tools MCP Server',
      transports: ['http']
    });
  });

  // 메인 MCP 엔드포인트 (Streamable HTTP)
  app.post('/mcp', async (req, res) => {
    try {
      // 세션 ID 확인
      const sessionId = req.headers['mcp-session-id'] as string | undefined;
      let transport: StreamableHTTPServerTransport;

      if (sessionId && transports[sessionId]) {
        // 기존 세션 재사용
        transport = transports[sessionId];
      } else if (isInitializeRequest(req.body)) {
        // 새로운 초기화 요청 (세션 ID 유무와 관계없이)
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: (newSessionId) => {
            transports[newSessionId] = transport;
            console.log(`✅ 새 세션 생성: ${newSessionId}`);
          }
        });

        // 세션 종료 시 정리
        transport.onclose = () => {
          if (transport.sessionId) {
            delete transports[transport.sessionId];
            console.log(`🗑️  세션 종료: ${transport.sessionId}`);
          }
        };

        // 서버 생성 및 연결
        const server = createMCPServer();
        await server.connect(transport);
      } else {
        // Stateless 모드: 매 요청마다 새로운 transport 생성
        console.log('🔄 Stateless 모드: 새로운 transport 생성');
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => '', // stateless 모드 (빈 문자열)
        });

        // 서버 생성 및 연결
        const server = createMCPServer();
        await server.connect(transport);
      }

      // 요청 처리
      await transport.handleRequest(req, res, req.body);
    } catch (error) {
      console.error('MCP 요청 처리 실패:', error);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: '2.0',
          error: {
            code: -32603,
            message: config.showDetailedErrors ? `Internal server error: ${error}` : 'Internal server error',
          },
          id: null,
        });
      }
    }
  });

  // SSE 엔드포인트 제거: Streamable HTTP 전용 사용

  // 헬스 체크 엔드포인트
  app.get('/health', (_req, res) => {
    res.json({
      status: 'healthy',
      version: '1.13.2',
      timestamp: new Date().toISOString(),
      activeSessions: Object.keys(transports).length
    });
  });

  // 서버 시작
  const server = app.listen(config.port, () => {
    console.log(`� GeOn MCP Server v1.13.2 running at http://localhost:${config.port}`);
    console.log(`� MCP endpoint: http://localhost:${config.port}/mcp`);
    console.log(`🔐 Auth server: ${config.authServerUrl}`);
    console.log(`📋 Available tools: OAuth 기반 도구들`);
    console.log(`🛡️  Auth disabled: ${config.disableAuth}`);
    console.log(`🚀 Server is ready to accept connections`);
  });

  // 서버 에러 처리
  server.on('error', (error) => {
    console.error('Server error:', error);
  });

  // 연결 처리 로깅
  server.on('connection', (socket) => {
    console.log('📡 New connection established');
    socket.on('close', () => {
      console.log('📡 Connection closed');
    });
  });

  // 서버가 계속 실행되도록 무한 대기
  await new Promise<void>((resolve, reject) => {
    server.on('close', resolve);
    server.on('error', reject);

    // 프로세스 종료 시그널 처리
    process.on('SIGINT', () => {
      console.log('\n🛑 Received SIGINT, shutting down gracefully...');
      server.close(() => resolve());
    });

    process.on('SIGTERM', () => {
      console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
      server.close(() => resolve());
    });
  });
}

// 프로세스 종료 방지
process.on('SIGINT', () => {
  console.log('\n🛑 Server shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Server shutting down gracefully...');
  process.exit(0);
});

main().catch((error) => {
  console.error('Server error:', error);
  process.exit(1);
});