# MCP 도구 개발 가이드라인

## 개요

이 문서는 GeOn MCP 서버에서 새로운 도구를 개발할 때 따라야 할 표준 패턴과 가이드라인을 정의합니다.

## 아키텍처 구조

```
src/
├── tools/           # 도구 정의
│   └── definitions.ts
├── handlers/        # 도구 실행 핸들러
│   └── oauth-tools.ts
├── server/          # MCP 서버 로직
│   ├── mcp-server.ts
│   └── oauth-middleware.ts
├── utils/           # 유틸리티 함수
│   └── auth-fetch.ts
└── index.ts         # 서버 진입점
```

## MCP 표준 준수

### 1. 도구 정의 구조

모든 도구는 [MCP 공식 문서](https://modelcontextprotocol.io/docs/concepts/tools)의 Tool definition structure를 따라야 합니다:

```typescript
interface MCPToolDefinition {
  name: string;          // 고유 식별자
  description: string;   // 사람이 읽을 수 있는 설명
  inputSchema: any;      // JSON Schema for parameters
  annotations?: {        // 도구 동작에 대한 힌트
    title?: string;
    readOnlyHint?: boolean;
    destructiveHint?: boolean;
    idempotentHint?: boolean;
    openWorldHint?: boolean;
  };
  requiresAuth?: boolean; // OAuth 인증 요구사항 (확장)
}
```

### 2. 도구 분류

#### 인증이 필요한 도구 (`requiresAuth: true`)
- 사용자별 데이터에 접근하는 도구
- 데이터를 수정하는 도구
- 개인정보를 포함하는 API 호출

#### 인증이 불필요한 도구 (`requiresAuth: false`)
- 공개 데이터 조회
- 계산 도구
- 정적 정보 제공

## 새로운 도구 추가 절차

### 1단계: 도구 정의 추가

`src/tools/definitions.ts`에 새로운 도구를 추가:

```typescript
export const mcpTools: Record<string, MCPToolDefinition> = {
  // 기존 도구들...
  
  newTool: {
    name: 'newTool',
    description: `도구 설명...`,
    inputSchema: zodToJsonSchema(z.object({
      param1: z.string().describe("파라미터 설명"),
      param2: z.number().optional().describe("선택적 파라미터"),
    })),
    annotations: {
      title: "도구 제목",
      readOnlyHint: true,  // 환경을 수정하지 않음
      destructiveHint: false,
      idempotentHint: true,
      openWorldHint: true, // 외부 시스템과 상호작용
    },
    requiresAuth: true, // 인증 요구사항
  },
};
```

### 2단계: 핸들러 함수 구현

`src/handlers/oauth-tools.ts`에 실행 함수를 추가:

```typescript
export async function executeNewTool(args: any, extra: any): Promise<CallToolResult> {
  try {
    // 1. 입력 파라미터 검증
    const { param1, param2 = defaultValue } = args || {};

    // 2. 도구 정의에서 인증 요구사항 확인
    const toolDef = getToolDefinition('newTool');
    const requiresAuth = toolDef?.requiresAuth ?? false;

    // 3. OAuth 토큰 추출
    const authToken = extractAuthToken(extra);

    // 4. API 호출
    const response = await smartFetch(
      `${baseUrl}/api/endpoint`,
      requiresAuth,
      authToken,
      { method: 'GET' }
    );

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }

    const data = await response.json();
    return createSuccessResponse(data);
    
  } catch (error: any) {
    return createErrorResponse(error);
  }
}
```

### 3단계: 라우터에 등록

`executeOAuthTool` 함수에 새로운 도구를 추가:

```typescript
export async function executeOAuthTool(
  toolName: string, 
  args: any, 
  extra: any
): Promise<CallToolResult> {
  switch (toolName) {
    case 'getLayerList':
      return await executeGetLayerList(args, extra);
    
    case 'newTool':
      return await executeNewTool(args, extra);
    
    default:
      return createErrorResponse(`OAuth tool '${toolName}' not found`);
  }
}
```

## 인증 처리 패턴

### OAuth 토큰 패스쓰루 방식

```typescript
// 1. 토큰 추출
const authToken = extractAuthToken(extra);

// 2. 스마트 fetch 사용
const response = await smartFetch(
  url,
  requiresAuth,
  authToken,
  options
);
```

### 필수 헤더

인증이 필요한 API 요청 시 다음 헤더가 자동으로 설정됩니다:

```typescript
{
  'Content-Type': 'application/json',
  'X-Auth-Type': 'token',
  'Authorization': 'Bearer <token>'
}
```

## 에러 처리 패턴

### 표준화된 에러 응답

```typescript
function createErrorResponse(error: Error | string): CallToolResult {
  const errorMessage = error instanceof Error ? error.message : error;
  console.error('Tool execution error:', errorMessage);
  
  return {
    content: [
      {
        type: 'text',
        text: `Error: ${errorMessage}`,
      },
    ],
    isError: true,
  };
}
```

### 성공 응답

```typescript
function createSuccessResponse(data: any): CallToolResult {
  return {
    content: [
      {
        type: 'text',
        text: JSON.stringify(data, null, 2),
      },
    ],
  };
}
```

## 베스트 프랙티스

1. **명확한 도구 이름**: 동사 + 명사 형태 (예: `getLayerList`, `createProject`)
2. **상세한 설명**: 기능, 사용 예시, 제한사항 포함
3. **적절한 annotations**: 도구의 특성을 정확히 표현
4. **입력 검증**: Zod 스키마를 사용한 타입 안전성
5. **로깅**: 디버깅을 위한 적절한 로그 출력
6. **에러 처리**: 사용자 친화적인 에러 메시지

## 테스트

새로운 도구를 추가한 후:

1. 빌드 확인: `pnpm build`
2. 서버 실행: `node dist/index.js`
3. ai-docs 앱에서 도구 목록 확인
4. 도구 실행 테스트
5. 에러 케이스 테스트
