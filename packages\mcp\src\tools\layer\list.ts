import { z } from 'zod';
import { createMCPTool } from '../shared/utils.js';
import { LayerSchemas, CommonSchemas } from '../shared/validators.js';
import { createGeonSmtClient } from '@geon-query/model/restapi/smt';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

function getApiUserId(): string {
  return "geonuser"; // 항상 geonuser 계정 사용
}

/**
 * 레이어 목록 조회 도구
 */
export const getLayerListTool: AISDKToolWrapper = {
  description: `레이어 목록을 검색합니다.

기능:
- 레이어 이름으로 검색하거나 전체 목록을 조회할 수 있습니다
- 레이어 타입별 필터링 (점, 선, 면)
- 데이터 구분별 필터링 (사용자, 공유, 국가 데이터 등)
- 페이징 지원

레이어 타입 매핑 (lyrTySeCode):
- '1': 점 레이어
- '2': 선 레이어  
- '3': 면 레이어
- 비어있는 경우: 전체 타입

데이터 구분 매핑 (holdDataSeCode):
- '0': 전체
- '1': 사용자 데이터
- '2': 공유 데이터
- '9': 국가 데이터

사용 예시:
- 전체 목록: {"userId": "geonuser"}
- 특정 레이어 검색: {"userId": "geonuser", "layerName": "도로"}
- 점 레이어만: {"userId": "geonuser", "lyrTySeCode": "1"}`,

  parameters: z.object({
    userId: z.string().describe("사용자 ID (기본값: geonuser)"),
    layerName: z.string().optional().describe("레이어 이름"),
    lyrTySeCode: LayerSchemas.layerType.optional().describe("레이어 유형 코드"),
    holdDataSeCode: LayerSchemas.dataType.optional().default("0").describe("데이터 구분 코드"),
    pageIndex: CommonSchemas.pagination.shape.pageIndex,
    pageSize: CommonSchemas.pagination.shape.pageSize,
  }),

  execute: async ({
    userId,
    layerName = "",
    lyrTySeCode = "",
    holdDataSeCode = "0",
    pageIndex = "1",
    pageSize = "10",
  }) => {
    try {
      // geon-query/model 클라이언트 생성
      const apiKey = process.env.GEON_API_KEY || "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";
      const baseUrl = process.env.GEON_API_BASE_URL || "https://city.geon.kr/api/";

      const builderClient = createGeonSmtClient({
        baseUrl,
        crtfckey: apiKey,
      });

      // API 호출 파라미터 구성
      const requestParams = {
        userId: getApiUserId(), // 항상 geonuser 계정 사용
        holdDataSeCode,
        pageIndex,
        pageSize,
        crtfckey: apiKey,
      } as any;

      // layerName이 있을 경우에만 searchTxt 파라미터 추가
      if (layerName && layerName.trim() !== "") {
        requestParams.searchTxt = layerName.trim();
      }

      if (lyrTySeCode && lyrTySeCode.trim() !== "") {
        requestParams.lyrTySeCode = lyrTySeCode.trim();
      }

      // 레이어 목록 조회 API 호출
      const data = await builderClient.layer.infoList(requestParams);

      if (!data || !data.result) {
        return { error: "레이어 목록 조회 실패: 응답 데이터가 없습니다." };
      }

      return data;
    } catch (error: any) {
      console.error("레이어 목록 조회 실패:", error);
      return { error: `레이어 목록 조회 실패: ${error.message}` };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    if (!result.result?.layerList?.length) {
      return [
        {
          type: "text" as const,
          text: "검색 조건에 맞는 레이어가 없습니다.",
        },
      ];
    }

    const layers = result.result.layerList;
    const pageInfo = result.result.pageInfo;

    const layerList = layers
      .slice(0, 10) // 최대 10개만 표시
      .map((layer: any, index: number) => 
        `${index + 1}. ${layer.layerName} (ID: ${layer.layerId})\n   - 타입: ${getLayerTypeText(layer.lyrTySeCode)}\n   - 등록일: ${layer.regDt}`
      )
      .join("\n\n");

    const summary = `레이어 목록 조회 완료: 총 ${pageInfo.totalCount}개 중 ${layers.length}개 표시`;

    return [
      {
        type: "text" as const,
        text: `${summary}\n\n${layerList}`,
      },
    ];
  },
};

/**
 * 레이어 삭제 도구  
 */
export const removeLayerTool: AISDKToolWrapper = {
  description: `지도에서 레이어를 삭제합니다. 삭제된 레이어는 복구할 수 없으므로 신중하게 사용하세요.

사용 방법:
- layerId: 삭제할 레이어의 ID를 정확히 입력하세요
- Current map state에서 레이어 ID를 확인할 수 있습니다

사용 예시:
- "스타벅스 레이어를 삭제해줘" → {"layerId": "LR0000001234"}
- "이 레이어를 지워줘" → 현재 활성 레이어의 ID 사용
- "모든 레이어를 삭제해줘" → 각 레이어 ID를 순차적으로 삭제`,

  parameters: z.object({
    layerId: CommonSchemas.layerId,
    description: z.string().optional().describe("삭제 사유 또는 설명"),
  }),

  execute: async ({ layerId, description }) => {
    try {
      return {
        success: true,
        layerId,
        description: description || "레이어가 삭제되었습니다.",
      };
    } catch (error: any) {
      console.error("레이어 삭제 실패:", error);
      return {
        success: false,
        error: `레이어 삭제 실패: ${error.message}`,
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: result.description,
      },
    ];
  },
};

/**
 * 레이어 타입 코드를 텍스트로 변환하는 유틸리티
 */
function getLayerTypeText(code: string): string {
  switch (code) {
    case "1": return "점 레이어";
    case "2": return "선 레이어"; 
    case "3": return "면 레이어";
    default: return "알 수 없는 타입";
  }
}

/**
 * MCP 도구 정의들
 */
export const getLayerListDefinition: MCPToolDefinition = createMCPTool(
  "getLayerList",
  getLayerListTool,
  {
    title: "레이어 목록 조회",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);

export const removeLayerDefinition: MCPToolDefinition = createMCPTool(
  "removeLayer",
  removeLayerTool,
  {
    title: "레이어 삭제",
    readOnlyHint: false,
    destructiveHint: true,
    idempotentHint: false,
  }
);