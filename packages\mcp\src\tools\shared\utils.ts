import { zodToJsonSchema } from 'zod-to-json-schema';
import { z } from 'zod';
import { MCPToolDefinition, ToolResult, AISDKToolWrapper } from './types.js';

/**
 * AI SDK 도구를 MCP 형식으로 변환하는 유틸리티
 */
export function createMCPTool(
  name: string,
  aiTool: AISDKToolWrapper,
  options: {
    title?: string;
    readOnlyHint?: boolean;
    destructiveHint?: boolean;
    idempotentHint?: boolean;
    openWorldHint?: boolean;
  } = {}
): MCPToolDefinition {
  return {
    name,
    description: aiTool.description,
    inputSchema: zodToJsonSchema(aiTool.parameters),
    annotations: {
      title: options.title || name,
      readOnlyHint: options.readOnlyHint ?? true,
      destructiveHint: options.destructiveHint ?? false,
      idempotentHint: options.idempotentHint ?? true,
      openWorldHint: options.openWorldHint ?? true,
      ...options,
    },
  };
}

/**
 * 에러 응답을 생성하는 유틸리티
 */
export function createErrorResult(error: string | Error): ToolResult {
  const message = typeof error === 'string' ? error : error.message;
  return {
    content: [
      {
        type: "text",
        text: `오류: ${message}`,
      },
    ],
    isError: true,
  };
}

/**
 * 성공 응답을 생성하는 유틸리티
 */
export function createSuccessResult(message: string, data?: any): ToolResult {
  return {
    content: [
      {
        type: "text",
        text: data ? `${message}\n\n${JSON.stringify(data, null, 2)}` : message,
      },
    ],
    isError: false,
  };
}

/**
 * 텍스트 응답을 생성하는 유틸리티
 */
export function createTextResult(text: string): ToolResult {
  return {
    content: [
      {
        type: "text",
        text,
      },
    ],
  };
}

/**
 * CQL 조건 문자열 생성 헬퍼 함수
 */
export function buildCQLCondition(condition: {
  attributeName: string;
  operator: string;
  value: string;
}): string {
  const { attributeName, operator, value } = condition;

  // 공백과 따옴표 제거
  const cleanFieldName = attributeName.trim().replace(/['"]/g, "");

  if (operator.toUpperCase() === "LIKE") {
    return `"${cleanFieldName}" LIKE '%${value}%'`;
  } else if (operator.toUpperCase() === "IN") {
    const values = value.split(",").map((v) => v.trim());
    const valueStr = values.map((v) => `'${v}'`).join(",");
    return `"${cleanFieldName}" IN (${valueStr})`;
  } else {
    const isNumeric = !isNaN(Number(value));
    return `"${cleanFieldName}"${operator}${isNumeric ? value : `'${value}'`}`;
  }
}

/**
 * 거리 문자열 파싱 유틸리티
 */
export function parseDistance(distanceStr: string): number {
  const match = distanceStr.match(/^(\d+(?:\.\d+)?)\s*(m|km)?$/i);
  if (!match) {
    throw new Error(`잘못된 거리 형식: ${distanceStr}`);
  }

  const value = parseFloat(match[1]);
  const unit = match[2]?.toLowerCase() || "m";

  return unit === "km" ? value * 1000 : value;
}

/**
 * 주소 검색 결과를 LLM용으로 최적화하는 유틸리티
 */
export function optimizeAddressResult(result: any, maxResults = 3): Array<{
  type: "text";
  text: string;
}> {
  if (!result.result?.jusoList?.length) {
    return [
      {
        type: "text",
        text: "검색 결과가 없습니다. 다른 키워드로 다시 검색해보세요.",
      },
    ];
  }

  const addresses = result.result.jusoList;
  const addressCount = addresses.length;

  // 최대 개수까지만 LLM에 전달
  const topAddresses = addresses.slice(0, maxResults).map((addr: any) => ({
    roadAddr: addr.roadAddr,
    buildName: addr.buildName || addr.poiName,
    buildLo: addr.buildLo, // 경도 (X좌표)
    buildLa: addr.buildLa, // 위도 (Y좌표)
  }));

  const summary = `주소 검색이 완료되었습니다. 총 ${addressCount}개의 위치를 찾았습니다.`;

  let addressInfo = topAddresses
    .map(
      (addr: any, index: number) =>
        `${index + 1}. ${addr.roadAddr}${
          addr.buildName ? ` (${addr.buildName})` : ""
        } - 좌표: ${addr.buildLo},${addr.buildLa}`
    )
    .join("\n");

  if (addressCount > maxResults) {
    addressInfo += `\n... 외 ${addressCount - maxResults}개 추가 결과`;
  }

  return [
    {
      type: "text",
      text: `${summary}\n\n${addressInfo}`,
    },
  ];
}