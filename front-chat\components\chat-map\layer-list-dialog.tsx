"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { List, Search, Filter, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { BetterTooltip } from "../ui/tooltip";
import { toast } from "sonner";
import { useLayerManager } from "@/providers/tool-invocation-provider";
import { UseMapReturn } from "@geon-map/odf";

interface LayerItem {
  lyrId: string;
  lyrNm: string;
  lyrTySeCode: string;
  lyrTySeCodeNm: string;
  lyrClCodeNm: string;
  registDt: string;
  ownerNm: string;
}

interface LayerListResponse {
  code: number;
  message: string;
  result: {
    pageInfo: {
      pageSize: number;
      pageIndex: number;
      totalCount: number;
    };
    list: LayerItem[];
  };
}

const getLayerTypeColor = (typeCode: string) => {
  switch (typeCode) {
    case "1":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
    case "2":
      return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
    case "3":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
  }
};

export function LayerListDialog({ mapState }: { mapState?: UseMapReturn }) {
  const [open, setOpen] = useState(false);
  const [layers, setLayers] = useState<LayerItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageIndex, setPageIndex] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [addingLayers, setAddingLayers] = useState<Set<string>>(new Set());

  const { addLayer } = useLayerManager();

  // 레이어 추가 함수
  const handleAddLayer = async (layer: LayerItem) => {
    const layerId = layer.lyrId;

    // 이미 추가 중인 레이어인지 확인
    if (addingLayers.has(layerId)) {
      return;
    }

    setAddingLayers(prev => new Set(prev).add(layerId));

    try {
      // 서버 API를 통해 getLayer 도구 실행
      const response = await fetch('/api/layers/get-layer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          lyrId: layerId
        })
      });

      if (!response.ok) {
        throw new Error(`API 요청 실패: ${response.status}`);
      }

      const result = await response.json();

      if (result && result.id) {
        // 서울 건물통합정보 레이어인지 확인
        const isSeoulBuildingLayer = layer.lyrId === "LR0000004299" ||
          layer.lyrNm === "GIS건물통합정보_서울" ||
          result.name === "GIS건물통합정보_서울";

        // 레이어 매니저에 추가
        addLayer({
          id: result.id,
          name: result.name || layer.lyrNm,
          type: result.type || 'wms',
          visible: true,
          zIndex: 1,
          server: result.server,
          autoFit: isSeoulBuildingLayer ? false : true,
          layer: result.layer,
          service: result.service || 'wms',
          method: result.method || 'get',
          crtfckey: result.crtfckey,
          geometryType: result.geometryType,
          style: result.style,
          opacity: result.opacity || 1,

          toolCallId: `manual-add-${Date.now()}` // 수동 추가 표시
        });

        // 서울 건물통합정보 레이어인 경우 지도를 서울 중심으로 이동 (EPSG:5186 표준 서울 중심좌표)
        if (isSeoulBuildingLayer && mapState) {
          const center = new odf.Coordinate(955156.7761, 1951925.0984);
          const newZoom = 12;

          mapState.map.setCenter(center);
          mapState.map.setZoom(newZoom);
        }
        toast.success(`${layer.lyrNm} 레이어가 지도에 추가되었습니다`);


        setOpen(false); // 다이얼로그 닫기
      } else {
        throw new Error('레이어 정보를 가져올 수 없습니다');
      }
    } catch (error) {
      console.error('레이어 추가 실패:', error);
      toast.error(`${layer.lyrNm} 레이어 추가에 실패했습니다`);
    } finally {
      setAddingLayers(prev => {
        const newSet = new Set(prev);
        newSet.delete(layerId);
        return newSet;
      });
    }
  };

  const fetchLayers = useCallback(async (page: number, reset: boolean = false) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        userId: "geonuser",
        holdDataSeCode: "0",
        pageIndex: page.toString(),
        pageSize: "20",
      });

      if (searchTerm.trim()) {
        params.append("searchTxt", searchTerm.trim());
      }

      if (typeFilter && typeFilter !== "all") {
        params.append("lyrTySeCode", typeFilter);
      }

      // API 키 추가 (환경변수에서 가져오거나 기본값 사용)
      const apiKey = process.env.NEXT_PUBLIC_GEON_API_KEY || "";
      if (apiKey) {
        params.append("crtfckey", apiKey);
      }

      const response = await fetch(
        `/api/layers?${params.toString()}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data: LayerListResponse = await response.json();

      if (data.code === 200 && data.result) {
        const newLayers = data.result.list || [];
        setLayers(prev => {
          const updatedLayers = reset ? newLayers : [...prev, ...newLayers];
          // hasMore 계산을 setLayers 콜백 내부에서 수행
          setHasMore(newLayers.length === 20 && updatedLayers.length < data.result.pageInfo.totalCount);
          return updatedLayers;
        });
        setTotalCount(data.result.pageInfo.totalCount);
      }
    } catch (error) {
      console.error("Failed to fetch layers:", error);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, typeFilter]);

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollHeight - scrollTop <= clientHeight * 1.5 && hasMore && !loading) {
      setPageIndex(prev => prev + 1);
    }
  }, [hasMore, loading]);

  const handleSearch = useCallback(() => {
    // 검색 버튼 클릭 시 즉시 검색 (디바운싱 무시)
    setLayers([]);
    setPageIndex(1);
    setHasMore(true);
    fetchLayers(1, true);
  }, [fetchLayers]);

  const handleFilterChange = useCallback((value: string) => {
    setTypeFilter(value);
    setLayers([]);
    setPageIndex(1);
    setHasMore(true);
    // 필터 변경 시 즉시 새로운 데이터 로드
    setTimeout(() => fetchLayers(1, true), 0);
  }, [fetchLayers]);

  // 디바운싱된 검색 실행 함수
  const debouncedFetch = useCallback(() => {
    setLayers([]);
    setPageIndex(1);
    setHasMore(true);
    fetchLayers(1, true);
  }, [fetchLayers]);

  // Dialog가 열릴 때 초기 데이터 로드
  useEffect(() => {
    if (open) {
      setLayers([]);
      setPageIndex(1);
      setHasMore(true);
      fetchLayers(1, true);
    }
  }, [open]);

  // 디바운싱: searchTerm이 변경되면 500ms 후에 검색 실행 (Dialog가 열려있고, 초기 로드가 아닐 때만)
  useEffect(() => {
    if (!open || searchTerm === "") return; // Dialog가 닫혀있거나 검색어가 비어있으면 실행하지 않음

    const timer = setTimeout(() => {
      debouncedFetch();
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, debouncedFetch]);

  // 페이지네이션
  useEffect(() => {
    if (pageIndex > 1) {
      fetchLayers(pageIndex);
    }
  }, [pageIndex]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-9 flex items-center justify-start gap-2 w-full bg-background/80 hover:bg-background/90 backdrop-blur-md shadow-sm border transition-all duration-300"
        >
          <List className="h-4 w-4" />
          <span className="text-sm font-medium">레이어 검색</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <List className="h-5 w-5" />
            레이어 목록
            {totalCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                총 {totalCount}개
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {/* 검색 및 필터 */}
        <div className="flex gap-2 mb-4">
          <div className="flex-1 relative">
            <Search className={cn(
              "absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4",
              loading ? "text-blue-500 animate-pulse" : "text-muted-foreground"
            )} />
            <Input
              placeholder="레이어 이름으로 검색..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="pl-10"
            />
          </div>
          <Select value={typeFilter} onValueChange={handleFilterChange}>
            <SelectTrigger className="w-32">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="타입" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">전체</SelectItem>
              <SelectItem value="1">점</SelectItem>
              <SelectItem value="2">선</SelectItem>
              <SelectItem value="3">면</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleSearch} disabled={loading}>
            검색
          </Button>
        </div>

        {/* 레이어 목록 */}
        <div className="flex-1 h-[400px] overflow-y-auto" onScroll={handleScroll}>
          <div className="space-y-2 pr-4">
            {layers.map((layer) => (
              <div
                key={layer.lyrId}
                className="p-3 border rounded-lg hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium truncate">{layer.lyrNm}</h4>
                      <Badge className={cn("text-xs", getLayerTypeColor(layer.lyrTySeCode))}>
                        {layer.lyrTySeCodeNm}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-1">
                      ID: {layer.lyrId}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span>{layer.lyrClCodeNm}</span>
                      <span>소유자: {layer.ownerNm}</span>
                      <span>{new Date(layer.registDt).toLocaleDateString()}</span>
                    </div>
                  </div>

                  {/* 추가 버튼 */}
                  <div className="flex-shrink-0 ml-3">
                    <BetterTooltip content="지도에 레이어 추가">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddLayer(layer)}
                        disabled={addingLayers.has(layer.lyrId)}
                        className="h-8 w-8 p-0"
                      >
                        {addingLayers.has(layer.lyrId) ? (
                          <div className="h-3 w-3 animate-spin rounded-full border border-current border-t-transparent" />
                        ) : (
                          <Plus className="h-3 w-3" />
                        )}
                      </Button>
                    </BetterTooltip>
                  </div>
                </div>
              </div>
            ))}

            {/* 로딩 스켈레톤 */}
            {loading && (
              <div className="space-y-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div key={i} className="p-3 border rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-5 w-12" />
                    </div>
                    <Skeleton className="h-3 w-32 mb-1" />
                    <div className="flex gap-4">
                      <Skeleton className="h-3 w-20" />
                      <Skeleton className="h-3 w-16" />
                      <Skeleton className="h-3 w-24" />
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* 더 이상 데이터가 없을 때 */}
            {!loading && !hasMore && layers.length > 0 && (
              <div className="text-center py-4 text-muted-foreground">
                모든 레이어를 불러왔습니다.
              </div>
            )}

            {/* 데이터가 없을 때 */}
            {!loading && layers.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                검색 결과가 없습니다.
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
