/**
 * API 설정 및 인증 정보를 관리하는 유틸리티
 */

export interface ApiConfig {
  baseUrl: string;
  headers: {
    crtfckey: string;
  };
  auth: {
    userId: string;
    password: string;
  };
}

/**
 * API 요청을 위한 설정을 반환합니다.
 * 프론트엔드 로그인과 별개로 백엔드 API 요청 시에는 항상 geonuser 계정을 사용합니다.
 */
export const getApiConfig = (): ApiConfig => {
  const baseUrl =
    process.env.GEON_API_BASE_URL || "https://gsapi.geon.kr";

  // MCP 서버 자체 API 키 사용 (클라이언트 토큰과 별개)
  const apiKey = process.env.GEON_API_KEY;
  if (!apiKey) {
    console.warn("GEON_API_KEY가 설정되지 않았습니다.");
  }

  // 백엔드 API 요청용 계정 정보 (환경변수에서 가져오거나 기본값 사용)
  const apiUserId = process.env.GEON_API_USER_ID || 'geonuser';
  const apiUserPassword = process.env.GEON_API_USER_PASSWORD || 'wavus1234!';

  return {
    baseUrl,
    headers: {
      crtfckey: apiKey || "",
    },
    auth: {
      userId: apiUserId,
      password: apiUserPassword,
    },
  };
};

/**
 * API 요청 시 사용할 URLSearchParams에 인증 정보를 추가합니다.
 */
export const addAuthToParams = (params: URLSearchParams, config?: ApiConfig): URLSearchParams => {
  const apiConfig = config || getApiConfig();
  
  // API 키 추가
  if (apiConfig.headers.crtfckey) {
    params.append("crtfckey", apiConfig.headers.crtfckey);
  }
  
  return params;
};

/**
 * API 요청 시 사용할 헤더를 반환합니다.
 */
export const getApiHeaders = (config?: ApiConfig): Record<string, string> => {
  const apiConfig = config || getApiConfig();
  
  return {
    "Content-Type": "application/json",
    ...apiConfig.headers,
  };
};

/**
 * 백엔드 API 요청에서 사용할 userId를 반환합니다.
 * 프론트엔드 로그인 계정과 관계없이 항상 geonuser를 사용합니다.
 */
export const getApiUserId = (config?: ApiConfig): string => {
  const apiConfig = config || getApiConfig();
  return apiConfig.auth.userId;
};
