import { z } from 'zod';
import { createMCPTool, optimizeAddressResult } from '../shared/utils.js';
import { AddressSchemas } from '../shared/validators.js';
import { createGeonAddrgeoClient } from '@geon-query/model/restapi/addrgeo';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 주소 검색 도구 (통합)
 * searchAddress, searchOrigin, searchDestination의 기반이 되는 도구
 */
function createSearchTool(
  toolName: string,
  description: string,
  contextDescription: string
): AISDKToolWrapper {
  return {
    description: `${description}

기능:
- 주소 검색 (도로명주소, 지번주소)
- 장소명 검색 (POI 검색)
- 좌표 정보 반환
- ${contextDescription}

사용 예시:
- {"keyword": "서울시청"}
- {"keyword": "서울특별시 중구 세종대로 110"}`,
    
    parameters: z.object({
      keyword: AddressSchemas.keyword,
      showMultipleResults: z.boolean().optional().default(false).describe("다중 결과 표시 여부"),
      targetSrid: AddressSchemas.srid,
      countPerPage: z.number().min(1).max(20).optional().default(10).describe("페이지당 결과 수"),
      currentPage: z.number().min(1).optional().default(1).describe("현재 페이지"),
    }),

    execute: async (args) => {
      try {
        // geon-query/model 클라이언트 생성
        const apiKey = process.env.GEON_API_KEY || "UxizIdSqCePz93ViFt8ghZFFJuOzvUp0";
        const baseUrl = process.env.GEON_API_BASE_URL || "https://city.geon.kr/api/";

        const addressClient = createGeonAddrgeoClient({
          baseUrl,
          crtfckey: apiKey,
        });

        // 파라미터 기본값 처리
        const requestParams = {
          keyword: args.keyword,
          showMultipleResults: args.showMultipleResults ?? false,
          targetSrid: args.targetSrid ?? 4326,
          countPerPage: args.countPerPage ?? 10,
          currentPage: args.currentPage ?? 1,
        };

        console.log(`${toolName} API 호출 파라미터:`, requestParams);

        // 통합 주소 검색 API 호출
        const result = await addressClient.address.int(requestParams);

        return result;
      } catch (error: any) {
        console.error(`${toolName} 실행 실패:`, error);
        return {
          error: `${contextDescription} 실패: ${error.message}`,
        };
      }
    },

    experimental_toToolResultContent: (result: any) => {
      if (result.error) {
        return [{ type: "text" as const, text: result.error }];
      }
      
      return optimizeAddressResult(result);
    },
  };
}

/**
 * 일반 주소 검색 도구
 */
export const searchAddressTool = createSearchTool(
  "searchAddress",
  "주소나 장소명으로 위치를 검색합니다.",
  "위치 검색"
);

/**
 * 출발지 검색 도구
 */
export const searchOriginTool = createSearchTool(
  "searchOrigin", 
  "출발지 위치를 검색합니다. 경로 탐색의 시작점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",
  "출발지 검색"
);

/**
 * 목적지 검색 도구
 */
export const searchDestinationTool = createSearchTool(
  "searchDestination",
  "목적지 위치를 검색합니다. 경로 탐색의 도착점이 되는 주소나 건물명을 검색하여 좌표 정보를 얻습니다.",
  "목적지 검색"
);

/**
 * MCP 도구 정의들
 */
export const searchAddressDefinition: MCPToolDefinition = createMCPTool(
  "searchAddress", 
  searchAddressTool,
  {
    title: "주소 검색",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);

export const searchOriginDefinition: MCPToolDefinition = createMCPTool(
  "searchOrigin",
  searchOriginTool, 
  {
    title: "출발지 검색",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);

export const searchDestinationDefinition: MCPToolDefinition = createMCPTool(
  "searchDestination",
  searchDestinationTool,
  {
    title: "목적지 검색", 
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
  }
);