"use client";

import { type APIRequestType, type EstateClient } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@geon-ui/react/primitives/table";
import React from "react";

export default function LandBasicTable({
  pnu,
  crtfckey,
  client,
  pageNo,
  numOfRows,
}: APIRequestType<EstateClient["land"]["basic"]> & { client: EstateClient }) {
  // query
  const { data, isError, error, isLoading } = useAppQuery({
    queryKey: ["land/basic", pnu],
    queryFn: () => client.land.basic({ pnu, crtfckey, pageNo, numOfRows }),
  });
  // format result
  const result = React.useMemo(() => {
    if (!data) return undefined;
    return data.result as {
      resultList: {
        regstrSeCodeNm: string;
        lndcgrCodeNm: string;
        lndpclAr: string;
        posesnSeCodeNm: string;
        [k: string]: string;
      }[];
    };
  }, [data]);
  // error handling
  if (isError)
    return (
      <div className="flex justify-center align-middle">
        Error loading land data: {error as string}
      </div>
    );

  return (
    <Table className="w-full">
      <TableHeader>
        <TableRow>
          <TableHead className="font-bold text-center">대장</TableHead>
          <TableHead className="font-bold text-center">지목</TableHead>
          <TableHead className="font-bold text-center">소유구분</TableHead>
          <TableHead className="font-bold text-center">
            면적(m<sup>2</sup>)
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          {isLoading ? (
            // If is loading:
            <TableCell colSpan={4} className="text-center">
              Loading ...
            </TableCell>
          ) : result ? (
            // If there is result:
            <React.Fragment>
              <TableCell className="text-center">
                {result.resultList[0]!.regstrSeCodeNm}
              </TableCell>
              <TableCell className="text-center">
                {result.resultList[0]!.lndcgrCodeNm}
              </TableCell>
              <TableCell className="text-center">
                {result.resultList[0]!.posesnSeCodeNm}
              </TableCell>
              <TableCell className="text-right">
                {result.resultList[0]!.lndpclAr}
              </TableCell>
            </React.Fragment>
          ) : (
            // If there is no result:
            <TableCell colSpan={4} className="text-center">
              No Data
            </TableCell>
          )}
        </TableRow>
      </TableBody>
    </Table>
  );
}
