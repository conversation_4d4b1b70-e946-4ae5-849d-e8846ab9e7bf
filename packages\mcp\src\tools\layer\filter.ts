import { z } from 'zod';
import { createMCPTool, buildCQLCondition } from '../shared/utils.js';
import { CommonSchemas } from '../shared/validators.js';
import type { MCPToolDefinition, AISDKToolWrapper } from '../shared/types.js';

/**
 * 필터 조건 생성을 위한 LLM 호출 (임시)
 * TODO: 실제 환경에서는 적절한 LLM 호출 로직으로 교체 필요
 */
async function generateFilterConditionsWithLLM(
  userInstruction: string,
  attributes: string
): Promise<any> {
  // 임시로 간단한 필터 조건 생성 로직
  // 실제로는 OpenAI나 다른 LLM을 호출해야 함
  
  const conditions = [];
  let logicalOperator = "AND";

  // 사용자 요청에 따른 간단한 파싱 로직
  if (userInstruction.includes("서울")) {
    conditions.push({
      attributeName: attributes.includes("c1") ? "c1" : "주소",
      operator: "LIKE",
      value: "서울"
    });
  }

  if (userInstruction.includes("이상")) {
    const match = userInstruction.match(/(\d+)\s*이상/);
    if (match) {
      conditions.push({
        attributeName: attributes.includes("a1") ? "a1" : "값",
        operator: ">=",
        value: match[1]
      });
    }
  }

  if (userInstruction.includes("또는") || userInstruction.includes("이거나")) {
    logicalOperator = "OR";
  }

  return {
    conditions: conditions.length > 0 ? conditions : [
      {
        attributeName: attributes.includes("c1") ? "c1" : "기본속성",
        operator: "LIKE",
        value: "전체"
      }
    ],
    logicalOperator
  };
}

/**
 * 레이어 필터 생성 도구
 */
export const createLayerFilterTool: AISDKToolWrapper = {
  description: `레이어의 속성정보를 기반으로 CQL 필터를 생성합니다. 단일 조건과 복수 조건을 모두 지원합니다.

필드 선택 가이드:
- getLayerAttributes 도구로 조회한 properties의 필드명을 사용하세요
- 필드명은 정확히 일치해야 합니다 (대소문자 구분)

연산자 사용 가이드:
- 텍스트: LIKE (부분일치), = (완전일치)
- 숫자: >, >=, <, <=, =
- 목록: IN

복수 조건 지원:
- AND: 모든 조건이 참이어야 함
- OR: 하나 이상의 조건이 참이면 됨
- 예시: "서울이면서 인구 100만 이상", "강남구 또는 서초구"

중요 사항:
- attributes 매개변수로 사용 가능한 속성 정보를 반드시 제공해야 합니다
- 속성 정보는 'c1(주소), c2(건물명)' 형태로 제공하세요

사용 예시:
- {"lyrId": "LR123", "attributes": "c1(주소), c2(건물명)", "userInstruction": "서울이면서 고층 건물"}
- {"lyrId": "LR456", "attributes": "a1(인구), a2(면적)", "userInstruction": "인구 100만 이상"}`,

  parameters: z.object({
    lyrId: z.string().min(1).describe("필터를 적용할 레이어 ID"),
    attributes: z
      .string()
      .describe("🚨 필수: 속성명과 설명을 모두 포함한 형태로 제공 🚨 - 형식: 'c1(주소), c2(건물명)' - 절대 'c1'처럼 속성명만 제공하지 마세요"),
    userInstruction: z
      .string()
      .describe("사용자의 자연어 필터링 요청 (예: '서울이면서 인구 100만 이상', '강남구 또는 서초구', '건폐율 50 이상')"),
  }),

  execute: async ({ lyrId, attributes, userInstruction }) => {
    try {
      // LLM을 사용하여 필터 조건 생성 (임시 구현)
      const filterConditions = await generateFilterConditionsWithLLM(
        userInstruction,
        attributes
      );

      // CQL 필터 문자열 생성
      let filterStr = "";
      if (filterConditions.conditions.length === 0) {
        return { error: "필터 조건이 생성되지 않았습니다." };
      }

      if (filterConditions.conditions.length === 1) {
        // 단일 조건
        const condition = filterConditions.conditions[0];
        filterStr = buildCQLCondition(condition);
      } else {
        // 복수 조건
        const conditionStrings = filterConditions.conditions.map((condition: any) =>
          buildCQLCondition(condition)
        );
        const operator = filterConditions.logicalOperator || "AND";
        filterStr = conditionStrings.join(` ${operator} `);
      }

      return {
        success: true,
        lyr_id: lyrId,
        filter: filterStr,
        description: `${filterConditions.conditions.length}개 조건으로 필터링: ${userInstruction}`,
        conditions: filterConditions.conditions,
        logicalOperator: filterConditions.logicalOperator,
      };
    } catch (error: any) {
      console.error("필터 생성 실패:", error);
      return { 
        success: false,
        error: `필터 생성 실패: ${error.message}` 
      };
    }
  },

  experimental_toToolResultContent: (result: any) => {
    if (result.error) {
      return [{ type: "text" as const, text: result.error }];
    }

    return [
      {
        type: "text" as const,
        text: `${result.description}\n\n생성된 필터: ${result.filter}`,
      },
    ];
  },
};

/**
 * MCP 도구 정의
 */
export const createLayerFilterDefinition: MCPToolDefinition = createMCPTool(
  "createLayerFilter",
  createLayerFilterTool,
  {
    title: "레이어 필터 생성",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: false,
  }
);